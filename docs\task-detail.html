<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务详情 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 面包屑导航 -->
        <div style="margin-bottom: 1.5rem;">
            <nav style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--text-secondary);">
                <a href="dashboard.html" style="color: var(--primary-color); text-decoration: none;">仪表板</a>
                <i class="fas fa-chevron-right" style="font-size: 0.75rem;"></i>
                <a href="results.html" style="color: var(--primary-color); text-decoration: none;">结果管理</a>
                <i class="fas fa-chevron-right" style="font-size: 0.75rem;"></i>
                <span>任务详情</span>
            </nav>
        </div>

        <!-- 任务基本信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <div class="d-flex items-center gap-3">
                        <h2 style="font-size: 1.5rem; font-weight: 600; margin: 0;">人工智能芯片市场调研</h2>
                        <span class="badge badge-success">已完成</span>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-secondary" onclick="retryTask()">
                            <i class="fas fa-redo"></i> 重新执行
                        </button>
                        <button class="btn btn-primary" onclick="downloadResult()">
                            <i class="fas fa-download"></i> 下载结果
                        </button>
                        <button class="btn btn-success" onclick="shareTask()">
                            <i class="fas fa-share"></i> 分享
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem;">
                    <div>
                        <h4 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: 0.5rem; text-transform: uppercase;">任务信息</h4>
                        <div style="display: grid; gap: 0.75rem;">
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">任务ID:</span>
                                <span style="font-family: monospace;">#TASK-2024-001</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">创建时间:</span>
                                <span>2024-06-26 10:30</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">完成时间:</span>
                                <span>2024-06-26 10:35</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">执行时长:</span>
                                <span>5分钟12秒</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: 0.5rem; text-transform: uppercase;">配置信息</h4>
                        <div style="display: grid; gap: 0.75rem;">
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">AI模型:</span>
                                <span>GPT-4</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">搜索引擎:</span>
                                <span>Google, 百度</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">搜索深度:</span>
                                <span>标准搜索</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">语言偏好:</span>
                                <span>中英文并重</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 style="font-size: 0.875rem; font-weight: 600; color: var(--text-secondary); margin-bottom: 0.5rem; text-transform: uppercase;">结果统计</h4>
                        <div style="display: grid; gap: 0.75rem;">
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">搜索结果:</span>
                                <span>156条</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">分析报告:</span>
                                <span>2.3MB</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">质量评分:</span>
                                <span style="color: var(--success-color); font-weight: 600;">92分</span>
                            </div>
                            <div class="d-flex justify-between">
                                <span style="color: var(--text-secondary);">API调用:</span>
                                <span>23次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务执行日志 -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
            <div class="card">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-list-alt" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                        执行日志
                    </h3>
                </div>
                <div class="card-body" style="padding: 0;">
                    <div style="max-height: 400px; overflow-y: auto;">
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light);">
                            <div class="d-flex items-start gap-3">
                                <div style="width: 8px; height: 8px; background: var(--success-color); border-radius: 50%; margin-top: 0.5rem;"></div>
                                <div style="flex: 1;">
                                    <div class="d-flex justify-between items-start">
                                        <div>
                                            <p style="margin: 0; font-weight: 500;">任务完成</p>
                                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">AI分析报告生成完成，质量评分92分</p>
                                        </div>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">10:35:42</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light);">
                            <div class="d-flex items-start gap-3">
                                <div style="width: 8px; height: 8px; background: var(--primary-color); border-radius: 50%; margin-top: 0.5rem;"></div>
                                <div style="flex: 1;">
                                    <div class="d-flex justify-between items-start">
                                        <div>
                                            <p style="margin: 0; font-weight: 500;">AI分析中</p>
                                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">GPT-4正在分析搜索结果，生成调研报告</p>
                                        </div>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">10:33:15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light);">
                            <div class="d-flex items-start gap-3">
                                <div style="width: 8px; height: 8px; background: var(--success-color); border-radius: 50%; margin-top: 0.5rem;"></div>
                                <div style="flex: 1;">
                                    <div class="d-flex justify-between items-start">
                                        <div>
                                            <p style="margin: 0; font-weight: 500;">数据收集完成</p>
                                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">从Google和百度共收集到156条相关信息</p>
                                        </div>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">10:32:48</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light);">
                            <div class="d-flex items-start gap-3">
                                <div style="width: 8px; height: 8px; background: var(--warning-color); border-radius: 50%; margin-top: 0.5rem;"></div>
                                <div style="flex: 1;">
                                    <div class="d-flex justify-between items-start">
                                        <div>
                                            <p style="margin: 0; font-weight: 500;">搜索进行中</p>
                                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">正在Google搜索引擎中查找相关信息</p>
                                        </div>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">10:31:22</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div style="padding: 1rem;">
                            <div class="d-flex items-start gap-3">
                                <div style="width: 8px; height: 8px; background: var(--info-color); border-radius: 50%; margin-top: 0.5rem;"></div>
                                <div style="flex: 1;">
                                    <div class="d-flex justify-between items-start">
                                        <div>
                                            <p style="margin: 0; font-weight: 500;">任务开始</p>
                                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">开始执行关键词"人工智能芯片"的调研任务</p>
                                        </div>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">10:30:15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用的Prompt -->
            <div class="card">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-code" style="color: var(--warning-color); margin-right: 0.5rem;"></i>
                        使用的Prompt
                    </h3>
                </div>
                <div class="card-body">
                    <div style="background: var(--bg-secondary); padding: 1rem; border-radius: 8px; border: 1px solid var(--border-light);">
                        <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 0.875rem; line-height: 1.5; white-space: pre-wrap; color: var(--text-primary);">请基于搜索到的关于人工智能芯片的信息，从以下几个维度进行分析：

1. 市场规模和发展趋势
   - 当前市场规模
   - 预期增长率
   - 主要驱动因素

2. 主要参与者和竞争格局  
   - 领先企业分析
   - 市场份额分布
   - 竞争优势对比

3. 技术发展现状和未来方向
   - 核心技术突破
   - 性能指标对比
   - 技术发展路线图

4. 政策环境和监管情况
   - 相关政策支持
   - 监管要求
   - 国际贸易影响

5. 投资机会和风险评估
   - 投资热点领域
   - 潜在风险因素
   - 投资建议

请提供具体的数据支撑和客观的分析结论。</pre>
                    </div>
                    <div class="d-flex justify-between items-center mt-3">
                        <span style="font-size: 0.875rem; color: var(--text-secondary);">
                            <i class="fas fa-info-circle"></i> 字符数: 245 | Token估算: ~180
                        </span>
                        <button class="btn btn-secondary btn-sm" onclick="copyPrompt()">
                            <i class="fas fa-copy"></i> 复制
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分析结果预览 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-file-alt" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                        分析结果预览
                    </h3>
                    <div class="d-flex gap-2">
                        <button class="btn btn-secondary btn-sm" onclick="toggleFullResult()">
                            <i class="fas fa-expand"></i> 展开全文
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="downloadResult()">
                            <i class="fas fa-download"></i> 下载完整报告
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div style="background: var(--bg-primary); border: 1px solid var(--border-light); border-radius: 8px; padding: 1.5rem;">
                    <h4 style="color: var(--primary-color); margin-bottom: 1rem;">人工智能芯片市场调研报告</h4>
                    
                    <h5 style="color: var(--text-primary); margin: 1.5rem 0 0.75rem 0;">1. 市场规模和发展趋势</h5>
                    <p style="line-height: 1.6; margin-bottom: 1rem;">
                        根据最新市场数据显示，全球人工智能芯片市场在2023年达到了约180亿美元的规模，预计到2028年将增长至650亿美元，年复合增长率(CAGR)约为29.3%。主要驱动因素包括：
                    </p>
                    <ul style="margin-left: 1.5rem; margin-bottom: 1rem;">
                        <li>云计算和边缘计算需求的快速增长</li>
                        <li>自动驾驶汽车技术的发展</li>
                        <li>智能手机和IoT设备的AI功能需求</li>
                        <li>数据中心AI加速需求的增加</li>
                    </ul>
                    
                    <h5 style="color: var(--text-primary); margin: 1.5rem 0 0.75rem 0;">2. 主要参与者和竞争格局</h5>
                    <p style="line-height: 1.6; margin-bottom: 1rem;">
                        当前市场主要由以下几家公司主导：
                    </p>
                    <ul style="margin-left: 1.5rem; margin-bottom: 1rem;">
                        <li><strong>NVIDIA</strong> - 市场份额约40%，在GPU加速领域领先</li>
                        <li><strong>Intel</strong> - 市场份额约25%，传统CPU厂商转型AI</li>
                        <li><strong>AMD</strong> - 市场份额约15%，在数据中心AI加速方面快速增长</li>
                        <li><strong>Google (TPU)</strong> - 专用AI芯片，主要用于自家云服务</li>
                        <li><strong>华为海思</strong> - 在移动端AI芯片方面有所突破</li>
                    </ul>
                    
                    <div style="background: var(--bg-secondary); padding: 1rem; border-radius: 6px; margin-top: 1.5rem; text-align: center;">
                        <p style="margin: 0; color: var(--text-secondary); font-style: italic;">
                            <i class="fas fa-info-circle"></i> 这是报告的部分内容预览，完整报告包含更详细的分析和数据图表
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 任务操作功能
        function retryTask() {
            if (confirm('确定要重新执行这个任务吗？')) {
                AIResearchApp.showNotification('任务已重新提交到队列', 'success');
                // 这里可以调用API重新执行任务
            }
        }

        function downloadResult() {
            AIResearchApp.showNotification('正在准备下载文件...', 'info');
            setTimeout(() => {
                AIResearchApp.showNotification('报告下载已开始', 'success');
                // 这里可以触发实际的文件下载
            }, 1500);
        }

        function shareTask() {
            const shareUrl = window.location.href;
            navigator.clipboard.writeText(shareUrl).then(() => {
                AIResearchApp.showNotification('分享链接已复制到剪贴板', 'success');
            }).catch(() => {
                AIResearchApp.showNotification('复制失败，请手动复制链接', 'error');
            });
        }

        function copyPrompt() {
            const promptText = document.querySelector('pre').textContent;
            navigator.clipboard.writeText(promptText).then(() => {
                AIResearchApp.showNotification('Prompt已复制到剪贴板', 'success');
            }).catch(() => {
                AIResearchApp.showNotification('复制失败', 'error');
            });
        }

        function toggleFullResult() {
            AIResearchApp.showNotification('完整结果查看功能开发中', 'info');
        }

        // 页面加载时记录访问
        document.addEventListener('DOMContentLoaded', function() {
            if (window.AuthManager.isLoggedIn()) {
                window.AuthManager.logActivity('查看任务详情', { taskId: 'TASK-2024-001' });
            }
        });
    </script>
</body>
</html>
