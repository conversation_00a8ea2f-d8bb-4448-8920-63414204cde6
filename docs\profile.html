<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem;">
            <!-- 左侧用户信息 -->
            <div>
                <!-- 用户基本信息 -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div style="position: relative; display: inline-block; margin-bottom: 1rem;">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face" 
                                 alt="用户头像" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 4px solid var(--bg-secondary);">
                            <button class="btn btn-primary btn-sm" style="position: absolute; bottom: 0; right: 0; border-radius: 50%; width: 36px; height: 36px; padding: 0;" onclick="changeAvatar()">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <h3 style="margin: 0 0 0.5rem 0; font-size: 1.5rem;">张三</h3>
                        <p style="color: var(--text-secondary); margin: 0 0 1rem 0;">高级用户</p>
                        <div style="display: flex; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
                            <div class="text-center">
                                <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">127</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">今日搜索</div>
                            </div>
                            <div class="text-center">
                                <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">2,847</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">总搜索数</div>
                            </div>
                            <div class="text-center">
                                <div style="font-size: 1.25rem; font-weight: 600; color: var(--warning-color);">89</div>
                                <div style="font-size: 0.75rem; color: var(--text-secondary);">连续天数</div>
                            </div>
                        </div>
                        <button class="btn btn-primary w-full" onclick="editProfile()">
                            <i class="fas fa-edit"></i> 编辑资料
                        </button>
                    </div>
                </div>

                <!-- 使用统计 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-chart-bar" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                            使用统计
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-between items-center mb-3">
                            <span>用户角色</span>
                            <span class="user-role badge badge-primary">普通用户</span>
                        </div>
                        <div class="d-flex justify-between items-center mb-3">
                            <span>注册时间</span>
                            <span style="font-size: 0.875rem;">2024-01-15</span>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-between items-center mb-1">
                                <span style="font-size: 0.875rem;">本月API调用</span>
                                <span style="font-size: 0.875rem;">1,247次</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 65%; background-color: var(--primary-color);"></div>
                            </div>
                        </div>
                        <div class="d-flex justify-between items-center">
                            <span style="color: var(--text-secondary);">平均响应时间</span>
                            <span style="color: var(--success-color); font-weight: 600;">2.3秒</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">快速操作</h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; gap: 0.5rem;">
                            <button class="btn btn-secondary" style="justify-content: flex-start;" onclick="downloadData()">
                                <i class="fas fa-download"></i>
                                <span>下载我的数据</span>
                            </button>
                            <button class="btn btn-secondary" style="justify-content: flex-start;" onclick="changePassword()">
                                <i class="fas fa-key"></i>
                                <span>修改密码</span>
                            </button>
                            <button class="btn btn-secondary" style="justify-content: flex-start;" onclick="viewUsageStats()">
                                <i class="fas fa-chart-line"></i>
                                <span>使用统计</span>
                            </button>
                            <button class="btn btn-danger" style="justify-content: flex-start;" onclick="deleteAccount()">
                                <i class="fas fa-trash"></i>
                                <span>删除账户</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧详细信息 -->
            <div>
                <!-- 使用统计 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-chart-bar" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                            使用统计
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                            <div class="text-center p-3 border rounded">
                                <div style="font-size: 2rem; font-weight: 700; color: var(--primary-color); margin-bottom: 0.5rem;">2,847</div>
                                <div style="color: var(--text-secondary);">总搜索次数</div>
                            </div>
                            <div class="text-center p-3 border rounded">
                                <div style="font-size: 2rem; font-weight: 700; color: var(--success-color); margin-bottom: 0.5rem;">2,689</div>
                                <div style="color: var(--text-secondary);">成功次数</div>
                            </div>
                            <div class="text-center p-3 border rounded">
                                <div style="font-size: 2rem; font-weight: 700; color: var(--warning-color); margin-bottom: 0.5rem;">94.4%</div>
                                <div style="color: var(--text-secondary);">成功率</div>
                            </div>
                        </div>

                        <!-- 模拟图表 -->
                        <div style="height: 200px; background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 60%; background: linear-gradient(to top, var(--primary-color), transparent); opacity: 0.1;"></div>
                            <div style="text-align: center; z-index: 1;">
                                <i class="fas fa-chart-line" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                                <p style="color: var(--text-secondary); margin: 0;">最近30天使用趋势</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-history" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                            最近活动
                        </h3>
                    </div>
                    <div class="card-body" style="padding: 0;">
                        <div style="max-height: 300px; overflow-y: auto;">
                            <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                                <div style="width: 40px; height: 40px; background: rgb(59 130 246 / 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-search" style="color: var(--primary-color);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0; font-weight: 500;">搜索了"人工智能芯片"</p>
                                    <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">2分钟前</p>
                                </div>
                                <span class="badge badge-success">成功</span>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                                <div style="width: 40px; height: 40px; background: rgb(16 185 129 / 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-download" style="color: var(--success-color);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0; font-weight: 500;">导出了批量分析结果</p>
                                    <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">15分钟前</p>
                                </div>
                                <span class="badge badge-primary">完成</span>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                                <div style="width: 40px; height: 40px; background: rgb(245 158 11 / 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-upload" style="color: var(--warning-color);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0; font-weight: 500;">上传了Excel批量任务</p>
                                    <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">1小时前</p>
                                </div>
                                <span class="badge badge-warning">处理中</span>
                            </div>
                            <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                                <div style="width: 40px; height: 40px; background: rgb(6 182 212 / 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-cog" style="color: var(--info-color);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0; font-weight: 500;">更新了API配置</p>
                                    <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">2小时前</p>
                                </div>
                                <span class="badge badge-info">配置</span>
                            </div>
                            <div style="padding: 1rem; display: flex; items-center gap-3;">
                                <div style="width: 40px; height: 40px; background: rgb(239 68 68 / 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-exclamation-triangle" style="color: var(--danger-color);"></i>
                                </div>
                                <div style="flex: 1;">
                                    <p style="margin: 0; font-weight: 500;">搜索任务失败</p>
                                    <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">3小时前</p>
                                </div>
                                <span class="badge badge-danger">失败</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户安全 -->
                <div class="card">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-shield-alt" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                            账户安全
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; gap: 1rem;">
                            <div class="d-flex justify-between items-center p-3 border rounded">
                                <div class="d-flex items-center gap-3">
                                    <i class="fas fa-key" style="color: var(--success-color);"></i>
                                    <div>
                                        <strong>密码</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">上次修改: 2024-05-15</p>
                                    </div>
                                </div>
                                <button class="btn btn-secondary btn-sm" onclick="changePassword()">修改</button>
                            </div>
                            <div class="d-flex justify-between items-center p-3 border rounded">
                                <div class="d-flex items-center gap-3">
                                    <i class="fas fa-mobile-alt" style="color: var(--warning-color);"></i>
                                    <div>
                                        <strong>双因素认证</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">未启用</p>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm" onclick="enable2FA()">启用</button>
                            </div>
                            <div class="d-flex justify-between items-center p-3 border rounded">
                                <div class="d-flex items-center gap-3">
                                    <i class="fas fa-envelope" style="color: var(--success-color);"></i>
                                    <div>
                                        <strong>邮箱验证</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">zhang***@example.com</p>
                                    </div>
                                </div>
                                <span class="badge badge-success">已验证</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 个人中心相关功能
        function changeAvatar() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    AIResearchApp.showNotification('头像上传功能开发中', 'info');
                }
            };
            input.click();
        }

        function editProfile() {
            AIResearchApp.showNotification('编辑资料功能开发中', 'info');
        }

        function viewUsageStats() {
            AIResearchApp.showNotification('详细使用统计功能开发中', 'info');
        }

        function downloadData() {
            AIResearchApp.showNotification('正在准备数据下载...', 'info');
            setTimeout(() => {
                AIResearchApp.showNotification('数据下载已开始', 'success');
            }, 2000);
        }

        function changePassword() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div class="card" style="width: 400px; max-width: 90vw;">
                        <div class="card-header">
                            <h3>修改密码</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label class="form-label">当前密码</label>
                                <input type="password" class="form-control" placeholder="输入当前密码">
                            </div>
                            <div class="form-group">
                                <label class="form-label">新密码</label>
                                <input type="password" class="form-control" placeholder="输入新密码">
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认新密码</label>
                                <input type="password" class="form-control" placeholder="再次输入新密码">
                            </div>
                            <div class="d-flex justify-end gap-2">
                                <button class="btn btn-secondary" onclick="this.closest('div[style*=fixed]').remove()">取消</button>
                                <button class="btn btn-primary" onclick="savePassword(); this.closest('div[style*=fixed]').remove();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function savePassword() {
            AIResearchApp.showNotification('密码修改成功', 'success');
        }



        function deleteAccount() {
            if (confirm('确定要删除账户吗？此操作不可恢复，将删除所有数据。')) {
                AIResearchApp.showNotification('账户删除功能开发中', 'warning');
            }
        }

        function enable2FA() {
            AIResearchApp.showNotification('双因素认证功能开发中', 'info');
        }
    </script>
</body>
</html>
