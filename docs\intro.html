<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI调研助手 - 原型展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .device-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .device-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .device-btn:hover,
        .device-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
        }

        .prototypes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .prototype-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .prototype-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .prototype-header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .prototype-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .prototype-header p {
            opacity: 0.9;
            font-size: 0.875rem;
        }

        .device-frame {
            position: relative;
            margin: 1.5rem;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .device-frame.desktop {
            border: 8px solid #2d3748;
            border-radius: 15px;
        }

        .device-frame.tablet {
            border: 12px solid #2d3748;
            border-radius: 25px;
            max-width: 768px;
            margin: 1.5rem auto;
        }

        .device-frame.mobile {
            border: 15px solid #2d3748;
            border-radius: 30px;
            max-width: 375px;
            margin: 1.5rem auto;
        }

        .device-frame iframe {
            width: 100%;
            height: 500px;
            border: none;
            display: block;
        }

        .device-frame.tablet iframe {
            height: 600px;
        }

        .device-frame.mobile iframe {
            height: 700px;
        }

        .prototype-actions {
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 0 0.5rem;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .features {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 3rem;
            color: white;
        }

        .features h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .feature-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .feature-item i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #fbbf24;
        }

        .feature-item h3 {
            font-size: 1.125rem;
            margin-bottom: 0.5rem;
        }

        .feature-item p {
            opacity: 0.9;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .prototypes-grid {
                grid-template-columns: 1fr;
            }

            .device-selector {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1><i class="fas fa-brain"></i> AI调研助手</h1>
            <p>智能数据分析平台 - 高保真原型展示</p>
            
            <!-- 设备选择器 -->
            <div class="device-selector">
                <button class="device-btn active" onclick="switchDevice('desktop')">
                    <i class="fas fa-desktop"></i> 桌面端
                </button>
                <button class="device-btn" onclick="switchDevice('tablet')">
                    <i class="fas fa-tablet-alt"></i> 平板端
                </button>
                <button class="device-btn" onclick="switchDevice('mobile')">
                    <i class="fas fa-mobile-alt"></i> 手机端
                </button>
            </div>
        </div>

        <!-- 原型展示网格 -->
        <div class="prototypes-grid">
            <!-- 仪表板 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-chart-pie"></i> 仪表板</h3>
                    <p>数据概览、快速操作、最近任务</p>
                </div>
                <div class="device-frame desktop" id="dashboard-frame">
                    <iframe src="dashboard.html" title="仪表板"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="dashboard.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('dashboard-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 单次搜索 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-search"></i> 单次搜索</h3>
                    <p>关键词输入、AI分析配置、数据源选择</p>
                </div>
                <div class="device-frame desktop" id="search-frame">
                    <iframe src="search.html" title="单次搜索"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="search.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('search-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 批量任务 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-tasks"></i> 批量任务</h3>
                    <p>Excel上传、任务配置、进度管理</p>
                </div>
                <div class="device-frame desktop" id="batch-frame">
                    <iframe src="batch.html" title="批量任务"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="batch.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('batch-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 结果管理 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-table"></i> 结果管理</h3>
                    <p>数据表格、筛选排序、批量导出</p>
                </div>
                <div class="device-frame desktop" id="results-frame">
                    <iframe src="results.html" title="结果管理"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="results.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('results-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 设置配置 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-cog"></i> 设置配置</h3>
                    <p>API配置、搜索引擎、AI模型设置</p>
                </div>
                <div class="device-frame desktop" id="settings-frame">
                    <iframe src="settings.html" title="设置配置"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="settings.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('settings-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 个人中心 -->
            <div class="prototype-card">
                <div class="prototype-header">
                    <h3><i class="fas fa-user"></i> 个人中心</h3>
                    <p>用户信息、使用统计、账户安全</p>
                </div>
                <div class="device-frame desktop" id="profile-frame">
                    <iframe src="profile.html" title="个人中心"></iframe>
                </div>
                <div class="prototype-actions">
                    <a href="profile.html" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> 打开页面
                    </a>
                    <button class="btn btn-secondary" onclick="refreshFrame('profile-frame')">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 功能特性介绍 -->
        <div class="features">
            <h2>核心功能特性</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-robot"></i>
                    <h3>AI智能分析</h3>
                    <p>集成GPT-4、Claude等先进AI模型，提供深度数据分析和洞察</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-search"></i>
                    <h3>多源数据搜索</h3>
                    <p>支持Google、百度、必应等搜索引擎，可自定义数据源接入</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-tasks"></i>
                    <h3>批量处理</h3>
                    <p>Excel批量导入，支持大规模数据处理和并发任务管理</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-chart-bar"></i>
                    <h3>可视化展示</h3>
                    <p>丰富的图表和数据可视化，直观展示分析结果和趋势</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <h3>响应式设计</h3>
                    <p>完美适配桌面、平板、手机等各种设备，随时随地使用</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-shield-alt"></i>
                    <h3>安全可靠</h3>
                    <p>企业级安全保障，数据加密传输，支持私有化部署</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设备切换功能
        function switchDevice(deviceType) {
            // 更新按钮状态
            document.querySelectorAll('.device-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新所有设备框架
            document.querySelectorAll('.device-frame').forEach(frame => {
                frame.className = `device-frame ${deviceType}`;
                
                // 根据设备类型调整iframe高度
                const iframe = frame.querySelector('iframe');
                if (deviceType === 'mobile') {
                    iframe.style.height = '700px';
                } else if (deviceType === 'tablet') {
                    iframe.style.height = '600px';
                } else {
                    iframe.style.height = '500px';
                }
            });
        }

        // 刷新iframe
        function refreshFrame(frameId) {
            const frame = document.getElementById(frameId);
            const iframe = frame.querySelector('iframe');
            iframe.src = iframe.src;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载动画
            const cards = document.querySelectorAll('.prototype-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });

            // 监听iframe加载状态
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach(iframe => {
                iframe.addEventListener('load', function() {
                    this.style.opacity = '1';
                });
                iframe.style.opacity = '0.5';
                iframe.style.transition = 'opacity 0.3s ease';
            });
        });

        // 添加滚动视差效果
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.header');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });
    </script>
</body>
</html>
