// AI调研助手 - 导航管理模块

// 通用导航HTML模板
const SIDEBAR_TEMPLATE = `
<div class="sidebar-header">
    <div class="d-flex items-center gap-3">
        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
            <i class="fas fa-brain" style="color: white; font-size: 20px;"></i>
        </div>
        <div>
            <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI调研助手</h3>
            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">智能数据分析平台</p>
        </div>
    </div>
</div>

<nav class="sidebar-nav">
    <a href="dashboard.html" class="nav-link" data-page="dashboard">
        <i class="fas fa-chart-pie"></i>
        <span>仪表板</span>
    </a>
    <a href="search.html" class="nav-link" data-page="search" data-permission="basic_search">
        <i class="fas fa-search"></i>
        <span>单次搜索</span>
    </a>
    <a href="batch.html" class="nav-link" data-page="batch" data-permission="batch_tasks">
        <i class="fas fa-tasks"></i>
        <span>批量任务</span>
    </a>
    <a href="results.html" class="nav-link" data-page="results" data-permission="view_own_results">
        <i class="fas fa-table"></i>
        <span>结果管理</span>
    </a>
    <a href="task-history.html" class="nav-link" data-page="task-history" data-permission="view_own_results">
        <i class="fas fa-history"></i>
        <span>任务历史</span>
    </a>
    <a href="settings.html" class="nav-link" data-page="settings">
        <i class="fas fa-cog"></i>
        <span>设置配置</span>
    </a>
    <a href="admin.html" class="nav-link" data-page="admin" data-permission="system_admin">
        <i class="fas fa-shield-alt"></i>
        <span>系统管理</span>
    </a>
    <a href="profile.html" class="nav-link" data-page="profile">
        <i class="fas fa-user"></i>
        <span>个人中心</span>
    </a>
</nav>
`;

// 通用顶部导航HTML模板
const NAVBAR_TEMPLATE = `
<div class="d-flex justify-between items-center">
    <div class="d-flex items-center gap-4">
        <button class="btn btn-secondary d-none" data-sidebar-toggle style="display: none !important;">
            <i class="fas fa-bars"></i>
        </button>
        <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;" id="pageTitle">页面标题</h1>
    </div>
    <div class="d-flex items-center gap-3">
        <button class="btn btn-secondary" data-tooltip="通知">
            <i class="fas fa-bell"></i>
            <span class="badge badge-danger" style="position: absolute; top: -5px; right: -5px; min-width: 18px; height: 18px; border-radius: 50%; font-size: 10px; display: flex; align-items: center; justify-content: center;">3</span>
        </button>
        <div class="d-flex items-center gap-3">
            <div class="d-flex items-center gap-2">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                     alt="用户头像" class="user-avatar" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                <div>
                    <div class="user-name" style="font-size: 0.875rem; font-weight: 500;">用户名</div>
                    <div class="user-role badge badge-warning" style="font-size: 0.625rem; padding: 0.125rem 0.375rem;">角色</div>
                </div>
            </div>
            <button class="btn btn-secondary" onclick="logout()" data-tooltip="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </div>
</div>
`;

// 页面标题映射
const PAGE_TITLES = {
    'dashboard': '仪表板',
    'search': '单次搜索',
    'batch': '批量任务',
    'results': '结果管理',
    'task-history': '任务历史',
    'task-detail': '任务详情',
    'settings': '设置配置',
    'admin': '系统管理',
    'profile': '个人中心'
};

// 导航管理类
class NavigationManager {
    constructor() {
        this.currentPage = this.getCurrentPageName();
        this.init();
    }

    // 初始化导航
    init() {
        this.updateSidebar();
        this.updateNavbar();
        this.setActiveNavItem();
        this.setupEventListeners();
    }

    // 获取当前页面名称
    getCurrentPageName() {
        const path = window.location.pathname;
        const fileName = path.split('/').pop();
        return fileName.replace('.html', '');
    }

    // 更新侧边栏
    updateSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.innerHTML = SIDEBAR_TEMPLATE;
        }
    }

    // 更新顶部导航栏
    updateNavbar() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.innerHTML = NAVBAR_TEMPLATE;
            
            // 设置页面标题
            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle) {
                pageTitle.textContent = PAGE_TITLES[this.currentPage] || '页面';
            }
        }
    }

    // 设置当前页面的导航项为激活状态
    setActiveNavItem() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            const page = link.getAttribute('data-page');
            if (page === this.currentPage) {
                link.classList.add('active');
            }
        });
    }

    // 设置事件监听器
    setupEventListeners() {
        // 导航链接点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.nav-link')) {
                const link = e.target.closest('.nav-link');
                const permission = link.getAttribute('data-permission');
                
                // 检查权限
                if (permission && !window.AuthManager.hasPermission(permission)) {
                    e.preventDefault();
                    this.showAccessDenied();
                    return;
                }
                
                // 记录导航活动
                const page = link.getAttribute('data-page');
                window.AuthManager.logActivity('页面访问', { page: page });
            }
        });
    }

    // 显示访问被拒绝提示
    showAccessDenied() {
        if (window.AIResearchApp && window.AIResearchApp.showNotification) {
            window.AIResearchApp.showNotification('您没有权限访问此功能', 'warning');
        } else {
            alert('您没有权限访问此功能');
        }
    }

    // 更新用户信息显示
    updateUserInfo() {
        if (!window.AuthManager.isLoggedIn()) return;

        const user = window.AuthManager.getCurrentUser();
        
        // 更新用户头像
        const avatarElements = document.querySelectorAll('.user-avatar');
        avatarElements.forEach(element => {
            element.src = user.avatar;
            element.alt = user.name;
        });

        // 更新用户名称
        const nameElements = document.querySelectorAll('.user-name');
        nameElements.forEach(element => {
            element.textContent = user.name;
        });

        // 更新角色标识
        const roleElements = document.querySelectorAll('.user-role');
        roleElements.forEach(element => {
            const isAdmin = user.role === 'admin';
            element.textContent = isAdmin ? '管理员' : '普通用户';
            element.className = `user-role badge ${isAdmin ? 'badge-warning' : 'badge-primary'}`;
        });
    }

    // 控制导航项可见性
    controlNavVisibility() {
        if (!window.AuthManager.isLoggedIn()) return;

        // 控制基于权限的导航项
        const navItems = document.querySelectorAll('[data-permission]');
        navItems.forEach(item => {
            const requiredPermission = item.getAttribute('data-permission');
            if (!window.AuthManager.hasPermission(requiredPermission)) {
                item.style.display = 'none';
            }
        });

        // 控制基于角色的元素
        const adminElements = document.querySelectorAll('[data-role="admin"]');
        adminElements.forEach(element => {
            if (!window.AuthManager.isAdmin()) {
                element.style.display = 'none';
            }
        });

        const userElements = document.querySelectorAll('[data-role="user"]');
        userElements.forEach(element => {
            if (window.AuthManager.isAdmin()) {
                element.style.display = 'none';
            }
        });
    }

    // 添加面包屑导航
    addBreadcrumb(items) {
        const breadcrumbContainer = document.querySelector('.breadcrumb-container');
        if (!breadcrumbContainer) return;

        const breadcrumbHTML = items.map((item, index) => {
            const isLast = index === items.length - 1;
            if (isLast) {
                return `<span class="breadcrumb-item active">${item.name}</span>`;
            } else {
                return `<a href="${item.url}" class="breadcrumb-item">${item.name}</a>`;
            }
        }).join('<i class="fas fa-chevron-right breadcrumb-separator"></i>');

        breadcrumbContainer.innerHTML = `
            <nav class="breadcrumb">
                ${breadcrumbHTML}
            </nav>
        `;
    }

    // 高亮搜索结果
    highlightSearchResults(searchTerm) {
        if (!searchTerm) return;

        const navLinks = document.querySelectorAll('.nav-link span');
        navLinks.forEach(span => {
            const text = span.textContent;
            if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
                span.innerHTML = text.replace(
                    new RegExp(`(${searchTerm})`, 'gi'),
                    '<mark>$1</mark>'
                );
            }
        });
    }
}

// 全局退出登录函数
function logout() {
    if (confirm('确定要退出登录吗？')) {
        window.AuthManager.logActivity('用户登出');
        window.AuthManager.logout();
    }
}

// 创建全局导航管理器实例
window.NavigationManager = new NavigationManager();

// 页面加载完成后初始化导航
document.addEventListener('DOMContentLoaded', () => {
    // 等待权限管理器初始化完成
    setTimeout(() => {
        if (window.AuthManager.isLoggedIn()) {
            window.NavigationManager.updateUserInfo();
            window.NavigationManager.controlNavVisibility();
        }
    }, 100);
});

// 导出导航管理器
window.NavigationManager = NavigationManager;
