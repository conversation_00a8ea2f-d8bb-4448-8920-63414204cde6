<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置配置 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="d-flex items-center gap-3">
                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-brain" style="color: white; font-size: 20px;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI调研助手</h3>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">智能数据分析平台</p>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-link">
                <i class="fas fa-chart-pie"></i>
                <span>仪表板</span>
            </a>
            <a href="search.html" class="nav-link">
                <i class="fas fa-search"></i>
                <span>单次搜索</span>
            </a>
            <a href="batch.html" class="nav-link">
                <i class="fas fa-tasks"></i>
                <span>批量任务</span>
            </a>
            <a href="results.html" class="nav-link">
                <i class="fas fa-table"></i>
                <span>结果管理</span>
            </a>
            <a href="settings.html" class="nav-link active">
                <i class="fas fa-cog"></i>
                <span>设置配置</span>
            </a>
            <a href="profile.html" class="nav-link">
                <i class="fas fa-user"></i>
                <span>个人中心</span>
            </a>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar">
            <div class="d-flex justify-between items-center">
                <div class="d-flex items-center gap-4">
                    <button class="btn btn-secondary d-none" data-sidebar-toggle style="display: none !important;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">设置配置</h1>
                </div>
                <div class="d-flex items-center gap-3">
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i> 保存所有设置
                    </button>
                    <div class="d-flex items-center gap-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                             alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                        <span style="font-size: 0.875rem; font-weight: 500;">张三</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置导航标签 -->
        <div class="card mb-4">
            <div class="card-body" style="padding: 1rem;">
                <div class="d-flex gap-1" style="border-bottom: 1px solid var(--border-light);">
                    <button class="btn btn-primary tab-btn active" onclick="switchTab('api')" id="apiTab">
                        <i class="fas fa-key"></i> API配置
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('search')" id="searchTab">
                        <i class="fas fa-search"></i> 搜索引擎
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('ai')" id="aiTab">
                        <i class="fas fa-robot"></i> AI模型
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('datasource')" id="datasourceTab">
                        <i class="fas fa-database"></i> 数据源
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('general')" id="generalTab">
                        <i class="fas fa-cog"></i> 通用设置
                    </button>
                </div>
            </div>
        </div>

        <!-- API配置标签页 -->
        <div id="apiContent" class="tab-content">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-key" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        API密钥配置
                    </h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 2rem;">
                        <!-- OpenAI配置 -->
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 40px; height: 40px; background: #10a37f; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-brain" style="color: white;"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">OpenAI</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">GPT-4, GPT-3.5 等模型</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-success">已连接</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked>
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API Key</label>
                                <div class="d-flex gap-2">
                                    <input type="password" class="form-control" value="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" placeholder="输入OpenAI API Key">
                                    <button class="btn btn-secondary" onclick="togglePassword(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-primary" onclick="testConnection('openai')">
                                        <i class="fas fa-plug"></i> 测试
                                    </button>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">API基础URL</label>
                                    <input type="text" class="form-control" value="https://api.openai.com/v1" placeholder="API基础URL">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">请求超时(秒)</label>
                                    <input type="number" class="form-control" value="30" min="10" max="300">
                                </div>
                            </div>
                        </div>

                        <!-- Claude配置 -->
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 40px; height: 40px; background: #ff6b35; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-robot" style="color: white;"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">Anthropic Claude</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">Claude-3 等模型</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-warning">未配置</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API Key</label>
                                <div class="d-flex gap-2">
                                    <input type="password" class="form-control" placeholder="输入Anthropic API Key">
                                    <button class="btn btn-secondary" onclick="togglePassword(this)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-primary" onclick="testConnection('claude')">
                                        <i class="fas fa-plug"></i> 测试
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 自定义API配置 -->
                        <div style="border: 1px dashed var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div>
                                    <h4 style="margin: 0; font-size: 1.125rem;">自定义API</h4>
                                    <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">添加其他AI服务提供商</p>
                                </div>
                                <button class="btn btn-primary" onclick="addCustomAPI()">
                                    <i class="fas fa-plus"></i> 添加API
                                </button>
                            </div>
                            <div id="customAPIList">
                                <!-- 自定义API列表将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索引擎标签页 -->
        <div id="searchContent" class="tab-content" style="display: none;">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-search" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                        搜索引擎配置
                    </h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 1.5rem;">
                        <!-- Google搜索 -->
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <i class="fab fa-google" style="font-size: 2rem; color: #4285f4;"></i>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">Google Search</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">Google自定义搜索API</p>
                                    </div>
                                </div>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" checked>
                                    <span style="font-size: 0.875rem;">启用</span>
                                </label>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">API Key</label>
                                    <input type="password" class="form-control" placeholder="Google API Key">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">搜索引擎ID</label>
                                    <input type="text" class="form-control" placeholder="Custom Search Engine ID">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">每日配额限制</label>
                                <input type="number" class="form-control" value="100" min="1" max="10000">
                            </div>
                        </div>

                        <!-- 百度搜索 -->
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 32px; height: 32px; background: #2932e1; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-weight: bold; font-size: 0.875rem;">百</span>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">百度搜索</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">百度搜索API</p>
                                    </div>
                                </div>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" checked>
                                    <span style="font-size: 0.875rem;">启用</span>
                                </label>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">API Key</label>
                                    <input type="password" class="form-control" placeholder="百度API Key">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Secret Key</label>
                                    <input type="password" class="form-control" placeholder="百度Secret Key">
                                </div>
                            </div>
                        </div>

                        <!-- 必应搜索 -->
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <i class="fab fa-microsoft" style="font-size: 2rem; color: #00a1f1;"></i>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">Bing Search</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">微软必应搜索API</p>
                                    </div>
                                </div>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox">
                                    <span style="font-size: 0.875rem;">启用</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Subscription Key</label>
                                <input type="password" class="form-control" placeholder="Bing Search API Key">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI模型标签页 -->
        <div id="aiContent" class="tab-content" style="display: none;">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-robot" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                        AI模型配置
                    </h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 2rem;">
                        <!-- 模型优先级设置 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">模型优先级</h4>
                            <div style="background: var(--bg-secondary); border-radius: 8px; padding: 1rem;">
                                <p style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 1rem;">
                                    拖拽调整模型使用优先级，系统将按顺序尝试使用可用的模型
                                </p>
                                <div id="modelPriorityList" style="display: flex; flex-direction: column; gap: 0.5rem;">
                                    <div class="d-flex items-center gap-3 p-3 bg-white border rounded cursor-move">
                                        <i class="fas fa-grip-vertical" style="color: var(--text-muted);"></i>
                                        <div style="flex: 1;">
                                            <strong>GPT-4</strong>
                                            <span style="font-size: 0.875rem; color: var(--text-secondary); margin-left: 1rem;">最强分析能力</span>
                                        </div>
                                        <span class="badge badge-success">可用</span>
                                    </div>
                                    <div class="d-flex items-center gap-3 p-3 bg-white border rounded cursor-move">
                                        <i class="fas fa-grip-vertical" style="color: var(--text-muted);"></i>
                                        <div style="flex: 1;">
                                            <strong>Claude-3</strong>
                                            <span style="font-size: 0.875rem; color: var(--text-secondary); margin-left: 1rem;">深度理解能力</span>
                                        </div>
                                        <span class="badge badge-warning">未配置</span>
                                    </div>
                                    <div class="d-flex items-center gap-3 p-3 bg-white border rounded cursor-move">
                                        <i class="fas fa-grip-vertical" style="color: var(--text-muted);"></i>
                                        <div style="flex: 1;">
                                            <strong>GPT-3.5</strong>
                                            <span style="font-size: 0.875rem; color: var(--text-secondary); margin-left: 1rem;">快速响应</span>
                                        </div>
                                        <span class="badge badge-success">可用</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 模型参数设置 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">模型参数</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                                <div class="form-group">
                                    <label class="form-label">温度 (Temperature)</label>
                                    <input type="range" class="form-control" min="0" max="2" step="0.1" value="0.7" oninput="updateTemperature(this.value)">
                                    <div class="d-flex justify-between items-center mt-1">
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">保守</span>
                                        <span style="font-size: 0.875rem; font-weight: 500;" id="temperatureValue">0.7</span>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);">创新</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">最大Token数</label>
                                    <select class="form-control form-select">
                                        <option value="1000">1,000 tokens</option>
                                        <option value="2000" selected>2,000 tokens</option>
                                        <option value="4000">4,000 tokens</option>
                                        <option value="8000">8,000 tokens</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 模型使用统计 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">使用统计</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 style="margin: 0; color: var(--primary-color);">15,247</h5>
                                        <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--text-secondary);">GPT-4 调用次数</p>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 style="margin: 0; color: var(--success-color);">8,932</h5>
                                        <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--text-secondary);">GPT-3.5 调用次数</p>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h5 style="margin: 0; color: var(--warning-color);">$127.45</h5>
                                        <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--text-secondary);">本月费用</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据源标签页 -->
        <div id="datasourceContent" class="tab-content" style="display: none;">
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-database" style="color: var(--warning-color); margin-right: 0.5rem;"></i>
                            自定义数据源
                        </h3>
                        <button class="btn btn-primary" onclick="addDataSource()">
                            <i class="fas fa-plus"></i> 添加数据源
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="dataSourceList">
                        <!-- 示例数据源 -->
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="d-flex justify-between items-start">
                                    <div style="flex: 1;">
                                        <h5 style="margin: 0 0 0.5rem 0;">新闻API</h5>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">
                                            https://newsapi.org/v2/everything?q={keyword}&apiKey=xxx
                                        </p>
                                        <div class="d-flex items-center gap-3 mt-2">
                                            <span class="badge badge-success">已启用</span>
                                            <span style="font-size: 0.75rem; color: var(--text-secondary);">响应格式: JSON</span>
                                            <span style="font-size: 0.75rem; color: var(--text-secondary);">数据路径: articles</span>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-secondary btn-sm" onclick="editDataSource(1)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-primary btn-sm" onclick="testDataSource(1)">
                                            <i class="fas fa-plug"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteDataSource(1)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通用设置标签页 -->
        <div id="generalContent" class="tab-content" style="display: none;">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                        <i class="fas fa-cog" style="color: var(--text-secondary); margin-right: 0.5rem;"></i>
                        通用设置
                    </h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 2rem;">
                        <!-- 系统设置 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">系统设置</h4>
                            <div style="display: grid; gap: 1rem;">
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>自动保存结果</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">自动保存搜索和分析结果</p>
                                    </div>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked>
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>邮件通知</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">任务完成时发送邮件通知</p>
                                    </div>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>数据备份</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">定期备份用户数据和配置</p>
                                    </div>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked>
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 性能设置 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">性能设置</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">并发任务数</label>
                                    <select class="form-control form-select">
                                        <option value="1">1个任务</option>
                                        <option value="3" selected>3个任务</option>
                                        <option value="5">5个任务</option>
                                        <option value="10">10个任务</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">请求间隔(秒)</label>
                                    <input type="number" class="form-control" value="1" min="0.5" max="10" step="0.5">
                                </div>
                            </div>
                        </div>

                        <!-- 数据管理 -->
                        <div>
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">数据管理</h4>
                            <div style="display: grid; gap: 1rem;">
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>清理历史数据</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">清理30天前的搜索结果</p>
                                    </div>
                                    <button class="btn btn-warning btn-sm" onclick="cleanOldData()">
                                        <i class="fas fa-broom"></i> 清理
                                    </button>
                                </div>
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>导出所有数据</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">导出用户数据和配置</p>
                                    </div>
                                    <button class="btn btn-primary btn-sm" onclick="exportAllData()">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                                <div class="d-flex justify-between items-center p-3 border rounded">
                                    <div>
                                        <strong>重置所有设置</strong>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">恢复到默认配置</p>
                                    </div>
                                    <button class="btn btn-danger btn-sm" onclick="resetAllSettings()">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/app.js"></script>
    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });

            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
                btn.classList.remove('active');
            });

            // 显示选中的标签页内容
            document.getElementById(tabName + 'Content').style.display = 'block';

            // 激活选中的标签按钮
            const activeBtn = document.getElementById(tabName + 'Tab');
            activeBtn.classList.add('btn-primary');
            activeBtn.classList.remove('btn-secondary');
            activeBtn.classList.add('active');
        }

        // 密码显示/隐藏切换
        function togglePassword(button) {
            const input = button.previousElementSibling;
            const icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // 测试API连接
        function testConnection(apiType) {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 测试中...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                if (apiType === 'openai') {
                    AIResearchApp.showNotification('OpenAI连接成功！', 'success');
                } else if (apiType === 'claude') {
                    AIResearchApp.showNotification('Claude连接失败，请检查API Key', 'error');
                } else {
                    AIResearchApp.showNotification(`${apiType}连接测试完成`, 'info');
                }
            }, 2000);
        }

        // 添加自定义API
        function addCustomAPI() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div class="card" style="width: 600px; max-width: 90vw;">
                        <div class="card-header">
                            <h3>添加自定义API</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label class="form-label">API名称</label>
                                <input type="text" class="form-control" placeholder="输入API名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">API端点</label>
                                <input type="text" class="form-control" placeholder="https://api.example.com/v1/chat">
                            </div>
                            <div class="form-group">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" placeholder="输入API Key">
                            </div>
                            <div class="d-flex justify-end gap-2">
                                <button class="btn btn-secondary" onclick="this.closest('div[style*=fixed]').remove()">取消</button>
                                <button class="btn btn-primary" onclick="saveCustomAPI(); this.closest('div[style*=fixed]').remove();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveCustomAPI() {
            AIResearchApp.showNotification('自定义API已保存', 'success');
        }

        // 添加数据源
        function addDataSource() {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div class="card" style="width: 700px; max-width: 90vw;">
                        <div class="card-header">
                            <h3>添加数据源</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label class="form-label">数据源名称</label>
                                <input type="text" class="form-control" placeholder="输入数据源名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">API URL</label>
                                <input type="text" class="form-control" placeholder="https://api.example.com/search?q={keyword}">
                                <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                    使用 {keyword} 作为关键词占位符
                                </p>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div class="form-group">
                                    <label class="form-label">请求方法</label>
                                    <select class="form-control form-select">
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">响应格式</label>
                                    <select class="form-control form-select">
                                        <option value="json">JSON</option>
                                        <option value="xml">XML</option>
                                        <option value="html">HTML</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据路径</label>
                                <input type="text" class="form-control" placeholder="data.results">
                                <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                    指定响应数据中的结果路径，如: data.results 或 articles
                                </p>
                            </div>
                            <div class="d-flex justify-end gap-2">
                                <button class="btn btn-secondary" onclick="this.closest('div[style*=fixed]').remove()">取消</button>
                                <button class="btn btn-primary" onclick="saveDataSource(); this.closest('div[style*=fixed]').remove();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function saveDataSource() {
            AIResearchApp.showNotification('数据源已保存', 'success');
        }

        function editDataSource(id) {
            AIResearchApp.showNotification('编辑数据源功能开发中', 'info');
        }

        function testDataSource(id) {
            AIResearchApp.showNotification('正在测试数据源连接...', 'info');
            setTimeout(() => {
                AIResearchApp.showNotification('数据源连接成功！', 'success');
            }, 2000);
        }

        function deleteDataSource(id) {
            if (confirm('确定要删除这个数据源吗？')) {
                AIResearchApp.showNotification('数据源已删除', 'warning');
            }
        }

        // 温度值更新
        function updateTemperature(value) {
            document.getElementById('temperatureValue').textContent = value;
        }

        // 通用设置功能
        function cleanOldData() {
            if (confirm('确定要清理30天前的历史数据吗？此操作不可恢复。')) {
                AIResearchApp.showNotification('正在清理历史数据...', 'info');
                setTimeout(() => {
                    AIResearchApp.showNotification('历史数据清理完成', 'success');
                }, 3000);
            }
        }

        function exportAllData() {
            AIResearchApp.showNotification('正在导出数据...', 'info');
            setTimeout(() => {
                AIResearchApp.showNotification('数据导出完成', 'success');
            }, 2000);
        }

        function resetAllSettings() {
            if (confirm('确定要重置所有设置吗？此操作将恢复到默认配置，不可恢复。')) {
                AIResearchApp.showNotification('正在重置设置...', 'warning');
                setTimeout(() => {
                    AIResearchApp.showNotification('设置已重置为默认值', 'success');
                }, 2000);
            }
        }

        // 保存所有设置
        function saveAllSettings() {
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                AIResearchApp.showNotification('所有设置已保存', 'success');
            }, 2000);
        }
    </script>
</body>
</html>