<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单次搜索 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="d-flex items-center gap-3">
                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-brain" style="color: white; font-size: 20px;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI调研助手</h3>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">智能数据分析平台</p>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-link">
                <i class="fas fa-chart-pie"></i>
                <span>仪表板</span>
            </a>
            <a href="search.html" class="nav-link active">
                <i class="fas fa-search"></i>
                <span>单次搜索</span>
            </a>
            <a href="batch.html" class="nav-link">
                <i class="fas fa-tasks"></i>
                <span>批量任务</span>
            </a>
            <a href="results.html" class="nav-link">
                <i class="fas fa-table"></i>
                <span>结果管理</span>
            </a>
            <a href="settings.html" class="nav-link">
                <i class="fas fa-cog"></i>
                <span>设置配置</span>
            </a>
            <a href="profile.html" class="nav-link">
                <i class="fas fa-user"></i>
                <span>个人中心</span>
            </a>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar">
            <div class="d-flex justify-between items-center">
                <div class="d-flex items-center gap-4">
                    <button class="btn btn-secondary d-none" data-sidebar-toggle style="display: none !important;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">单次搜索</h1>
                </div>
                <div class="d-flex items-center gap-3">
                    <button class="btn btn-secondary" data-tooltip="帮助">
                        <i class="fas fa-question-circle"></i>
                    </button>
                    <div class="d-flex items-center gap-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                        <span style="font-size: 0.875rem; font-weight: 500;">张三</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div style="max-width: 800px; margin: 0 auto;">
            <form id="searchForm" data-validate>
                <!-- 关键词输入 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-search" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                            搜索关键词
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">关键词 *</label>
                            <input type="text" class="form-control" name="keyword" placeholder="请输入要搜索的关键词，如：人工智能、新能源汽车等" required>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin-top: 0.5rem;">
                                <i class="fas fa-info-circle"></i> 支持中英文关键词，建议使用具体的行业术语获得更精准的结果
                            </p>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">搜索深度</label>
                            <select class="form-control form-select" name="depth">
                                <option value="basic">基础搜索 (10-20条结果)</option>
                                <option value="standard" selected>标准搜索 (30-50条结果)</option>
                                <option value="deep">深度搜索 (50-100条结果)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- AI分析配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-robot" style="color: var(--success-color); margin-right: 0.5rem;"></i>
                            AI分析配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">AI模型选择</label>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                <label class="d-flex items-center gap-2 p-3 border rounded cursor-pointer" style="border: 2px solid var(--primary-color); background: rgb(59 130 246 / 0.05);">
                                    <input type="radio" name="aiModel" value="gpt4" checked>
                                    <div>
                                        <div style="font-weight: 500;">GPT-4</div>
                                        <div style="font-size: 0.75rem; color: var(--text-secondary);">最强分析能力</div>
                                    </div>
                                </label>
                                <label class="d-flex items-center gap-2 p-3 border rounded cursor-pointer">
                                    <input type="radio" name="aiModel" value="gpt35">
                                    <div>
                                        <div style="font-weight: 500;">GPT-3.5</div>
                                        <div style="font-size: 0.75rem; color: var(--text-secondary);">快速响应</div>
                                    </div>
                                </label>
                                <label class="d-flex items-center gap-2 p-3 border rounded cursor-pointer">
                                    <input type="radio" name="aiModel" value="claude">
                                    <div>
                                        <div style="font-weight: 500;">Claude</div>
                                        <div style="font-size: 0.75rem; color: var(--text-secondary);">深度理解</div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">自定义Prompt</label>
                            <textarea class="form-control" name="customPrompt" rows="4" placeholder="请描述您希望AI如何分析搜索结果...">请基于搜索到的信息，从以下几个维度进行分析：
1. 市场规模和发展趋势
2. 主要参与者和竞争格局  
3. 技术发展现状和未来方向
4. 政策环境和监管情况
5. 投资机会和风险评估

请提供具体的数据支撑和客观的分析结论。</textarea>
                            <div class="d-flex justify-between items-center mt-2">
                                <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">
                                    <i class="fas fa-lightbulb"></i> 可使用 {keyword} 变量引用搜索关键词
                                </p>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="useTemplate()">使用模板</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据源配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-database" style="color: var(--info-color); margin-right: 0.5rem;"></i>
                            数据源配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">搜索引擎选择</label>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="searchEngines" value="google" checked>
                                    <i class="fab fa-google" style="color: #4285f4;"></i>
                                    <span>Google</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="searchEngines" value="baidu" checked>
                                    <i class="fas fa-search" style="color: #2932e1;"></i>
                                    <span>百度</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="searchEngines" value="bing">
                                    <i class="fab fa-microsoft" style="color: #00a1f1;"></i>
                                    <span>必应</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="searchEngines" value="scholar">
                                    <i class="fas fa-graduation-cap" style="color: #4285f4;"></i>
                                    <span>学术搜索</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">自定义数据源</label>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control" placeholder="输入API端点URL，支持{keyword}变量" id="customApiUrl">
                                <button type="button" class="btn btn-secondary" onclick="addCustomSource()">
                                    <i class="fas fa-plus"></i> 添加
                                </button>
                            </div>
                            <div id="customSources" class="mt-2"></div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">语言偏好</label>
                            <select class="form-control form-select" name="language">
                                <option value="auto">自动检测</option>
                                <option value="zh">中文优先</option>
                                <option value="en">英文优先</option>
                                <option value="both">中英文并重</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 输出配置 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">
                            <i class="fas fa-file-export" style="color: var(--warning-color); margin-right: 0.5rem;"></i>
                            输出配置
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">输出格式</label>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem;">
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="summary" checked>
                                    <span>摘要报告</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="detailed" checked>
                                    <span>详细分析</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="data">
                                    <span>原始数据</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="outputFormat" value="chart">
                                    <span>图表可视化</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">保存选项</label>
                            <div class="d-flex items-center gap-4">
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="project" checked>
                                    <span>保存到项目</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="export">
                                    <span>自动导出</span>
                                </label>
                                <label class="d-flex items-center gap-2">
                                    <input type="checkbox" name="saveOptions" value="email">
                                    <span>邮件通知</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex justify-between items-center gap-3">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="saveTemplate()">
                            <i class="fas fa-save"></i> 保存模板
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="loadTemplate()">
                            <i class="fas fa-folder-open"></i> 加载模板
                        </button>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-rocket"></i> 开始搜索分析
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/app.js"></script>
    <script>
        // 页面特定的JavaScript
        function useTemplate() {
            const templates = [
                "请基于搜索到的关于{keyword}的信息，从市场规模、技术趋势、竞争格局、政策环境等维度进行深度分析。",
                "分析{keyword}的发展现状、主要挑战、未来机遇，并提供投资建议和风险评估。",
                "研究{keyword}的技术发展路径、产业链分析、商业模式创新，给出战略建议。"
            ];
            
            const select = document.createElement('select');
            select.className = 'form-control mb-2';
            templates.forEach((template, index) => {
                const option = document.createElement('option');
                option.value = template;
                option.textContent = `模板 ${index + 1}`;
                select.appendChild(option);
            });
            
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div class="card" style="width: 500px; max-width: 90vw;">
                        <div class="card-header">
                            <h3>选择Prompt模板</h3>
                        </div>
                        <div class="card-body">
                            ${select.outerHTML}
                            <div class="d-flex justify-end gap-2">
                                <button class="btn btn-secondary" onclick="this.closest('div[style*=fixed]').remove()">取消</button>
                                <button class="btn btn-primary" onclick="document.querySelector('[name=customPrompt]').value = this.parentElement.parentElement.querySelector('select').value; this.closest('div[style*=fixed]').remove();">使用</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function addCustomSource() {
            const url = document.getElementById('customApiUrl').value;
            if (!url) return;
            
            const container = document.getElementById('customSources');
            const sourceDiv = document.createElement('div');
            sourceDiv.className = 'd-flex items-center gap-2 p-2 border rounded mb-2';
            sourceDiv.innerHTML = `
                <i class="fas fa-link"></i>
                <span style="flex: 1; font-size: 0.875rem;">${url}</span>
                <button type="button" class="btn btn-danger btn-sm" onclick="this.parentElement.remove()">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            container.appendChild(sourceDiv);
            document.getElementById('customApiUrl').value = '';
        }

        function saveTemplate() {
            AIResearchApp.showNotification('模板已保存', 'success');
        }

        function loadTemplate() {
            AIResearchApp.showNotification('模板加载功能开发中', 'info');
        }

        function resetForm() {
            if (confirm('确定要重置所有配置吗？')) {
                document.getElementById('searchForm').reset();
                document.getElementById('customSources').innerHTML = '';
            }
        }

        // 表单提交处理
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const keyword = formData.get('keyword');
            
            if (!keyword) {
                AIResearchApp.showNotification('请输入搜索关键词', 'warning');
                return;
            }
            
            // 模拟搜索过程
            const submitBtn = this.querySelector('button[type=submit]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                AIResearchApp.showNotification('搜索完成！正在跳转到结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = 'results.html';
                }, 1500);
            }, 3000);
        });

        // 单选框样式处理
        document.querySelectorAll('input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // 移除同组其他选项的选中样式
                document.querySelectorAll(`input[name="${this.name}"]`).forEach(r => {
                    r.closest('label').style.border = '1px solid var(--border-color)';
                    r.closest('label').style.background = 'var(--bg-primary)';
                });
                
                // 添加当前选项的选中样式
                if (this.checked) {
                    this.closest('label').style.border = '2px solid var(--primary-color)';
                    this.closest('label').style.background = 'rgb(59 130 246 / 0.05)';
                }
            });
        });
    </script>
</body>
</html>
