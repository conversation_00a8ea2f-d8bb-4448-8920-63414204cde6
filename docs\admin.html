<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 系统概览卡片 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">总用户数</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--primary-color);">24</h3>
                            <p style="font-size: 0.75rem; color: var(--success-color); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-arrow-up"></i> +3 本月新增
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(59 130 246 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-users" style="color: var(--primary-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">活跃用户</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--success-color);">18</h3>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-clock"></i> 最近7天
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(16 185 129 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-user-check" style="color: var(--success-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">系统负载</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--warning-color);">68%</h3>
                            <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-server"></i> CPU使用率
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(245 158 11 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-line" style="color: var(--warning-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-between items-start">
                        <div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem;">API调用量</p>
                            <h3 style="font-size: 2rem; font-weight: 700; margin: 0; color: var(--info-color);">156K</h3>
                            <p style="font-size: 0.75rem; color: var(--success-color); margin: 0.5rem 0 0 0;">
                                <i class="fas fa-arrow-up"></i> +12% 较上月
                            </p>
                        </div>
                        <div style="width: 48px; height: 48px; background: rgb(6 182 212 / 0.1); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-cloud" style="color: var(--info-color); font-size: 20px;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 管理功能标签页 -->
        <div class="card mb-4">
            <div class="card-body" style="padding: 1rem;">
                <div class="d-flex gap-1" style="border-bottom: 1px solid var(--border-light);">
                    <button class="btn btn-primary tab-btn active" onclick="switchTab('users')" id="usersTab">
                        <i class="fas fa-users"></i> 用户管理
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('models')" id="modelsTab">
                        <i class="fas fa-robot"></i> AI模型管理
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('engines')" id="enginesTab">
                        <i class="fas fa-search"></i> 搜索引擎管理
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('stats')" id="statsTab">
                        <i class="fas fa-chart-bar"></i> 使用统计
                    </button>
                    <button class="btn btn-secondary tab-btn" onclick="switchTab('logs')" id="logsTab">
                        <i class="fas fa-list-alt"></i> 系统日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户管理标签页 -->
        <div id="usersContent" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">用户管理</h3>
                        <button class="btn btn-primary" onclick="addUser()">
                            <i class="fas fa-plus"></i> 添加用户
                        </button>
                    </div>
                </div>
                <div class="card-body" style="padding: 0;">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>姓名</th>
                                    <th>角色</th>
                                    <th>邮箱</th>
                                    <th>最后登录</th>
                                    <th>状态</th>
                                    <th style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>admin</strong></td>
                                    <td>系统管理员</td>
                                    <td><span class="badge badge-warning">管理员</span></td>
                                    <td><EMAIL></td>
                                    <td>2024-06-26 11:30</td>
                                    <td><span class="badge badge-success">在线</span></td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-secondary btn-sm" onclick="editUser('admin')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="deleteUser('admin')" disabled>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>user</strong></td>
                                    <td>普通用户</td>
                                    <td><span class="badge badge-primary">普通用户</span></td>
                                    <td><EMAIL></td>
                                    <td>2024-06-26 10:15</td>
                                    <td><span class="badge badge-secondary">离线</span></td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-secondary btn-sm" onclick="editUser('user')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="deleteUser('user')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>researcher1</strong></td>
                                    <td>研究员张三</td>
                                    <td><span class="badge badge-primary">普通用户</span></td>
                                    <td><EMAIL></td>
                                    <td>2024-06-25 16:45</td>
                                    <td><span class="badge badge-secondary">离线</span></td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-secondary btn-sm" onclick="editUser('researcher1')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="deleteUser('researcher1')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI模型管理标签页 -->
        <div id="modelsContent" class="tab-content" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI模型管理</h3>
                        <button class="btn btn-primary" onclick="addModel()">
                            <i class="fas fa-plus"></i> 添加模型
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 1.5rem;">
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 40px; height: 40px; background: #10a37f; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-brain" style="color: white;"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">GPT-4</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">OpenAI最强模型</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-success">已启用</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked onchange="toggleModel('gpt4', this.checked)">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">调用次数</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">15,247</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">成功率</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">98.5%</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">平均响应时间</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--warning-color);">2.3s</div>
                                </div>
                            </div>
                        </div>

                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 40px; height: 40px; background: #10a37f; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-robot" style="color: white;"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">GPT-3.5</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">快速响应模型</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-success">已启用</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked onchange="toggleModel('gpt35', this.checked)">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">调用次数</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">8,932</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">成功率</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">99.2%</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">平均响应时间</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">1.1s</div>
                                </div>
                            </div>
                        </div>

                        <div style="border: 1px dashed var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 40px; height: 40px; background: #ff6b35; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-robot" style="color: white;"></i>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">Claude</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">深度理解模型</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-warning">未配置</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" onchange="toggleModel('claude', this.checked)">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                                <i class="fas fa-info-circle"></i> 需要配置API密钥才能启用此模型
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索引擎管理标签页 -->
        <div id="enginesContent" class="tab-content" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">搜索引擎管理</h3>
                        <button class="btn btn-primary" onclick="addEngine()">
                            <i class="fas fa-plus"></i> 添加搜索引擎
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 1.5rem;">
                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <i class="fab fa-google" style="font-size: 2rem; color: #4285f4;"></i>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">Google Search</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">Google自定义搜索API</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-success">已启用</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked onchange="toggleEngine('google', this.checked)">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">今日调用</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">1,247</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">配额使用</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--warning-color);">68%</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">成功率</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">96.5%</div>
                                </div>
                            </div>
                        </div>

                        <div style="border: 1px solid var(--border-color); border-radius: 8px; padding: 1.5rem;">
                            <div class="d-flex justify-between items-center mb-3">
                                <div class="d-flex items-center gap-3">
                                    <div style="width: 32px; height: 32px; background: #2932e1; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <span style="color: white; font-weight: bold; font-size: 0.875rem;">百</span>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">百度搜索</h4>
                                        <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">百度搜索API</p>
                                    </div>
                                </div>
                                <div class="d-flex items-center gap-2">
                                    <span class="badge badge-success">已启用</span>
                                    <label class="d-flex items-center gap-2">
                                        <input type="checkbox" checked onchange="toggleEngine('baidu', this.checked)">
                                        <span style="font-size: 0.875rem;">启用</span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">今日调用</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">892</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">配额使用</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">45%</div>
                                </div>
                                <div>
                                    <label style="font-size: 0.875rem; font-weight: 500;">成功率</label>
                                    <div style="font-size: 1.25rem; font-weight: 600; color: var(--success-color);">94.2%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用统计标签页 -->
        <div id="statsContent" class="tab-content" style="display: none;">
            <div style="display: grid; gap: 2rem;">
                <div class="card">
                    <div class="card-header">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">用户活跃度统计</h3>
                    </div>
                    <div class="card-body">
                        <div style="height: 300px; background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <div style="text-align: center;">
                                <i class="fas fa-chart-bar" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                                <p style="color: var(--text-secondary); margin: 0;">用户活跃度趋势图</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <div class="card">
                        <div class="card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI模型使用分布</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; gap: 1rem;">
                                <div class="d-flex justify-between items-center">
                                    <span>GPT-4</span>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" style="width: 65%; background-color: var(--primary-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">65%</span>
                                    </div>
                                </div>
                                <div class="d-flex justify-between items-center">
                                    <span>GPT-3.5</span>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" style="width: 35%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">35%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">搜索引擎使用分布</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: grid; gap: 1rem;">
                                <div class="d-flex justify-between items-center">
                                    <span>Google</span>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" style="width: 58%; background-color: #4285f4;"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">58%</span>
                                    </div>
                                </div>
                                <div class="d-flex justify-between items-center">
                                    <span>百度</span>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" style="width: 42%; background-color: #2932e1;"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">42%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统日志标签页 -->
        <div id="logsContent" class="tab-content" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-between items-center">
                        <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">系统日志</h3>
                        <div class="d-flex gap-2">
                            <select class="form-control form-select" style="width: auto;">
                                <option>全部日志</option>
                                <option>用户活动</option>
                                <option>系统错误</option>
                                <option>API调用</option>
                            </select>
                            <button class="btn btn-secondary" onclick="refreshLogs()">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body" style="padding: 0;">
                    <div style="max-height: 500px; overflow-y: auto;">
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                            <div style="width: 8px; height: 8px; background: var(--success-color); border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div class="d-flex justify-between items-start">
                                    <div>
                                        <p style="margin: 0; font-weight: 500;">用户登录</p>
                                        <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">用户 admin 成功登录系统</p>
                                    </div>
                                    <span style="font-size: 0.75rem; color: var(--text-secondary);">2024-06-26 11:30:15</span>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                            <div style="width: 8px; height: 8px; background: var(--primary-color); border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div class="d-flex justify-between items-start">
                                    <div>
                                        <p style="margin: 0; font-weight: 500;">API调用</p>
                                        <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">GPT-4 API调用成功，响应时间 2.3s</p>
                                    </div>
                                    <span style="font-size: 0.75rem; color: var(--text-secondary);">2024-06-26 11:28:42</span>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                            <div style="width: 8px; height: 8px; background: var(--warning-color); border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div class="d-flex justify-between items-start">
                                    <div>
                                        <p style="margin: 0; font-weight: 500;">系统警告</p>
                                        <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">Google API配额使用率达到68%</p>
                                    </div>
                                    <span style="font-size: 0.75rem; color: var(--text-secondary);">2024-06-26 11:25:18</span>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 1rem; border-bottom: 1px solid var(--border-light); display: flex; items-center gap-3;">
                            <div style="width: 8px; height: 8px; background: var(--danger-color); border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div class="d-flex justify-between items-start">
                                    <div>
                                        <p style="margin: 0; font-weight: 500;">API错误</p>
                                        <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">Claude API调用失败：API密钥无效</p>
                                    </div>
                                    <span style="font-size: 0.75rem; color: var(--text-secondary);">2024-06-26 11:20:33</span>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 1rem; display: flex; items-center gap-3;">
                            <div style="width: 8px; height: 8px; background: var(--info-color); border-radius: 50%;"></div>
                            <div style="flex: 1;">
                                <div class="d-flex justify-between items-start">
                                    <div>
                                        <p style="margin: 0; font-weight: 500;">批量任务</p>
                                        <p style="margin: 0; font-size: 0.75rem; color: var(--text-secondary);">用户 user 提交了包含50个关键词的批量任务</p>
                                    </div>
                                    <span style="font-size: 0.75rem; color: var(--text-secondary);">2024-06-26 11:15:07</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName + 'Content').style.display = 'block';
            
            // 激活选中的标签按钮
            const activeBtn = document.getElementById(tabName + 'Tab');
            activeBtn.classList.add('btn-primary');
            activeBtn.classList.remove('btn-secondary');
            activeBtn.classList.add('active');
        }

        // 用户管理功能
        function addUser() {
            AIResearchApp.showNotification('添加用户功能开发中', 'info');
        }

        function editUser(username) {
            AIResearchApp.showNotification(`编辑用户 ${username} 功能开发中`, 'info');
        }

        function deleteUser(username) {
            if (confirm(`确定要删除用户 ${username} 吗？`)) {
                AIResearchApp.showNotification(`用户 ${username} 已删除`, 'warning');
            }
        }

        // AI模型管理功能
        function addModel() {
            AIResearchApp.showNotification('添加AI模型功能开发中', 'info');
        }

        function toggleModel(modelId, enabled) {
            const status = enabled ? '启用' : '禁用';
            AIResearchApp.showNotification(`${modelId} 模型已${status}`, enabled ? 'success' : 'warning');
        }

        // 搜索引擎管理功能
        function addEngine() {
            AIResearchApp.showNotification('添加搜索引擎功能开发中', 'info');
        }

        function toggleEngine(engineId, enabled) {
            const status = enabled ? '启用' : '禁用';
            AIResearchApp.showNotification(`${engineId} 搜索引擎已${status}`, enabled ? 'success' : 'warning');
        }

        // 系统日志功能
        function refreshLogs() {
            AIResearchApp.showNotification('日志已刷新', 'success');
        }

        // 实时更新系统状态
        function updateSystemStats() {
            // 模拟实时数据更新
            const stats = [
                { selector: '.card-body h3', values: ['24', '18', '68%', '156K'] },
            ];

            // 这里可以调用API获取实时数据
            console.log('更新系统统计数据');
        }

        // 页面加载时检查管理员权限
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.AuthManager.hasPermission('system_admin')) {
                window.location.href = 'dashboard.html';
                return;
            }

            // 记录访问活动
            window.AuthManager.logActivity('访问系统管理页面');

            // 定期更新系统状态
            setInterval(updateSystemStats, 30000); // 每30秒更新一次
        });
    </script>
</body>
</html>
