<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务历史 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar"></div>

        <!-- 筛选工具栏 -->
        <div class="card mb-4">
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 150px 150px 150px auto; gap: 1rem; align-items: end;">
                    <div class="form-group mb-0">
                        <label class="form-label">搜索任务</label>
                        <input type="text" class="form-control" placeholder="搜索关键词、任务ID..." id="searchInput">
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">状态筛选</label>
                        <select class="form-control form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="completed">已完成</option>
                            <option value="processing">处理中</option>
                            <option value="failed">失败</option>
                            <option value="cancelled">已取消</option>
                            <option value="pending">排队中</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">时间范围</label>
                        <select class="form-control form-select" id="timeFilter">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">任务类型</label>
                        <select class="form-control form-select" id="typeFilter">
                            <option value="">全部类型</option>
                            <option value="single">单次搜索</option>
                            <option value="batch">批量任务</option>
                        </select>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--primary-color);">156</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">总任务数</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--success-color);">142</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">已完成</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--warning-color);">8</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">处理中</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--danger-color);">6</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">失败</p>
                </div>
            </div>
        </div>

        <!-- 任务历史列表 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">任务历史</h3>
                    <div class="d-flex items-center gap-3">
                        <span style="font-size: 0.875rem; color: var(--text-secondary);">
                            显示 1-10 条，共 156 条记录
                        </span>
                        <div class="d-flex gap-2">
                            <button class="btn btn-secondary btn-sm" onclick="exportHistory()">
                                <i class="fas fa-download"></i> 导出
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="clearHistory()">
                                <i class="fas fa-trash"></i> 清理历史
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body" style="padding: 0;">
                <div class="table-responsive">
                    <table class="table data-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th data-sort="keyword">关键词/任务名</th>
                                <th data-sort="type">类型</th>
                                <th data-sort="status">状态</th>
                                <th data-sort="model">AI模型</th>
                                <th data-sort="time">创建时间</th>
                                <th data-sort="duration">执行时长</th>
                                <th data-sort="score">质量评分</th>
                                <th style="width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="1"></td>
                                <td>
                                    <div>
                                        <strong>人工智能芯片</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">#TASK-2024-001</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-primary">单次搜索</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>GPT-4</td>
                                <td>2024-06-26 10:30</td>
                                <td>5分12秒</td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 50px;">
                                            <div class="progress-bar" style="width: 92%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">92</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" onclick="viewTask(1)" data-tooltip="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="downloadTask(1)" data-tooltip="下载">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="retryTask(1)" data-tooltip="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteTask(1)" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="2"></td>
                                <td>
                                    <div>
                                        <strong>新能源汽车市场分析</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">#TASK-2024-002</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-warning">批量任务</span></td>
                                <td><span class="badge badge-warning">处理中</span></td>
                                <td>GPT-3.5</td>
                                <td>2024-06-26 11:00</td>
                                <td>进行中...</td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" onclick="viewTask(2)" data-tooltip="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-warning btn-sm" onclick="pauseTask(2)" data-tooltip="暂停">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="cancelTask(2)" data-tooltip="取消">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="3"></td>
                                <td>
                                    <div>
                                        <strong>区块链技术发展</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">#TASK-2024-003</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-primary">单次搜索</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>GPT-4</td>
                                <td>2024-06-26 09:15</td>
                                <td>4分38秒</td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 50px;">
                                            <div class="progress-bar" style="width: 88%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">88</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" onclick="viewTask(3)" data-tooltip="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="downloadTask(3)" data-tooltip="下载">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="retryTask(3)" data-tooltip="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteTask(3)" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="4"></td>
                                <td>
                                    <div>
                                        <strong>元宇宙产业调研</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">#TASK-2024-004</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-primary">单次搜索</span></td>
                                <td><span class="badge badge-danger">失败</span></td>
                                <td>Claude</td>
                                <td>2024-06-26 08:45</td>
                                <td>-</td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-info btn-sm" onclick="viewError(4)" data-tooltip="查看错误">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="retryTask(4)" data-tooltip="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteTask(4)" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="taskSelect" value="5"></td>
                                <td>
                                    <div>
                                        <strong>5G通信技术</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">#TASK-2024-005</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-primary">单次搜索</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>GPT-3.5</td>
                                <td>2024-06-25 16:20</td>
                                <td>3分25秒</td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 50px;">
                                            <div class="progress-bar" style="width: 85%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">85</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" onclick="viewTask(5)" data-tooltip="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" onclick="downloadTask(5)" data-tooltip="下载">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" onclick="retryTask(5)" data-tooltip="重试">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteTask(5)" data-tooltip="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="card-footer">
                <div class="table-pagination d-flex justify-between items-center">
                    <div class="d-flex items-center gap-2">
                        <span style="font-size: 0.875rem;">每页显示:</span>
                        <select class="form-control form-select" style="width: auto;" onchange="changePageSize(this.value)">
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="d-flex justify-center items-center gap-2">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="btn btn-primary btn-sm">1</span>
                        <button class="btn btn-secondary btn-sm">2</button>
                        <button class="btn btn-secondary btn-sm">3</button>
                        <span>...</span>
                        <button class="btn btn-secondary btn-sm">16</button>
                        <button class="btn btn-secondary btn-sm">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/auth.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        // 筛选功能
        function applyFilters() {
            AIResearchApp.showNotification('筛选已应用', 'success');
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('timeFilter').value = '';
            document.getElementById('typeFilter').value = '';
            AIResearchApp.showNotification('筛选已重置', 'info');
        }

        // 任务操作
        function viewTask(id) {
            window.location.href = `task-detail.html?id=${id}`;
        }

        function downloadTask(id) {
            AIResearchApp.showNotification(`正在下载任务 ${id} 的结果`, 'success');
        }

        function retryTask(id) {
            if (confirm('确定要重新执行这个任务吗？')) {
                AIResearchApp.showNotification(`任务 ${id} 已重新提交`, 'success');
            }
        }

        function deleteTask(id) {
            if (confirm('确定要删除这个任务吗？此操作不可恢复。')) {
                AIResearchApp.showNotification(`任务 ${id} 已删除`, 'warning');
            }
        }

        function pauseTask(id) {
            AIResearchApp.showNotification(`任务 ${id} 已暂停`, 'info');
        }

        function cancelTask(id) {
            if (confirm('确定要取消这个任务吗？')) {
                AIResearchApp.showNotification(`任务 ${id} 已取消`, 'warning');
            }
        }

        function viewError(id) {
            AIResearchApp.showNotification('错误详情：Claude API密钥配置错误', 'error');
        }

        // 批量操作
        function exportHistory() {
            const selected = document.querySelectorAll('input[name="taskSelect"]:checked');
            if (selected.length === 0) {
                AIResearchApp.showNotification('请先选择要导出的任务', 'warning');
                return;
            }
            AIResearchApp.showNotification(`正在导出 ${selected.length} 个任务的历史记录`, 'success');
        }

        function clearHistory() {
            if (confirm('确定要清理历史记录吗？建议只清理30天前的记录。')) {
                AIResearchApp.showNotification('历史记录清理功能开发中', 'info');
            }
        }

        // 分页
        function changePageSize(size) {
            AIResearchApp.showNotification(`每页显示 ${size} 条记录`, 'info');
        }

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="taskSelect"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // 页面加载时记录访问
        document.addEventListener('DOMContentLoaded', function() {
            if (window.AuthManager.isLoggedIn()) {
                window.AuthManager.logActivity('查看任务历史');
            }
        });
    </script>
</body>
</html>
