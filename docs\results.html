<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果管理 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="d-flex items-center gap-3">
                <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-brain" style="color: white; font-size: 20px;"></i>
                </div>
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">AI调研助手</h3>
                    <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">智能数据分析平台</p>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.html" class="nav-link">
                <i class="fas fa-chart-pie"></i>
                <span>仪表板</span>
            </a>
            <a href="search.html" class="nav-link">
                <i class="fas fa-search"></i>
                <span>单次搜索</span>
            </a>
            <a href="batch.html" class="nav-link">
                <i class="fas fa-tasks"></i>
                <span>批量任务</span>
            </a>
            <a href="results.html" class="nav-link active">
                <i class="fas fa-table"></i>
                <span>结果管理</span>
            </a>
            <a href="settings.html" class="nav-link">
                <i class="fas fa-cog"></i>
                <span>设置配置</span>
            </a>
            <a href="profile.html" class="nav-link">
                <i class="fas fa-user"></i>
                <span>个人中心</span>
            </a>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="navbar">
            <div class="d-flex justify-between items-center">
                <div class="d-flex items-center gap-4">
                    <button class="btn btn-secondary d-none" data-sidebar-toggle style="display: none !important;">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 style="font-size: 1.5rem; font-weight: 600; margin: 0;">结果管理</h1>
                </div>
                <div class="d-flex items-center gap-3">
                    <button class="btn btn-success" onclick="exportSelected()">
                        <i class="fas fa-download"></i> 导出选中
                    </button>
                    <div class="d-flex items-center gap-2">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                             alt="用户头像" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                        <span style="font-size: 0.875rem; font-weight: 500;">张三</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索工具栏 -->
        <div class="card mb-4">
            <div class="card-body">
                <div style="display: grid; grid-template-columns: 1fr 200px 200px 200px auto; gap: 1rem; align-items: end;">
                    <div class="form-group mb-0">
                        <label class="form-label">搜索关键词</label>
                        <input type="text" class="form-control table-filter" placeholder="搜索关键词、分类、状态..." id="searchInput">
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">状态筛选</label>
                        <select class="form-control form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="completed">已完成</option>
                            <option value="processing">处理中</option>
                            <option value="failed">失败</option>
                            <option value="pending">待处理</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">时间范围</label>
                        <select class="form-control form-select" id="timeFilter">
                            <option value="">全部时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    
                    <div class="form-group mb-0">
                        <label class="form-label">分类筛选</label>
                        <select class="form-control form-select" id="categoryFilter">
                            <option value="">全部分类</option>
                            <option value="tech">科技</option>
                            <option value="finance">金融</option>
                            <option value="healthcare">医疗</option>
                            <option value="education">教育</option>
                        </select>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button class="btn btn-secondary" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--primary-color);">1,247</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">总结果数</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--success-color);">1,189</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">已完成</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--warning-color);">42</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">处理中</p>
                </div>
            </div>
            <div class="card">
                <div class="card-body text-center">
                    <h3 style="font-size: 1.5rem; font-weight: 700; margin: 0; color: var(--danger-color);">16</h3>
                    <p style="color: var(--text-secondary); margin: 0.5rem 0 0 0; font-size: 0.875rem;">失败</p>
                </div>
            </div>
        </div>

        <!-- 结果表格 -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-between items-center">
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin: 0;">调研结果</h3>
                    <div class="d-flex items-center gap-3">
                        <span style="font-size: 0.875rem; color: var(--text-secondary);">
                            显示 1-10 条，共 1,247 条结果
                        </span>
                        <div class="d-flex gap-2">
                            <button class="btn btn-secondary btn-sm" onclick="toggleView('table')" id="tableViewBtn">
                                <i class="fas fa-table"></i> 表格
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="toggleView('card')" id="cardViewBtn">
                                <i class="fas fa-th-large"></i> 卡片
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body" style="padding: 0;">
                <!-- 表格视图 -->
                <div id="tableView" class="table-responsive">
                    <table class="table data-table">
                        <thead>
                            <tr>
                                <th style="width: 40px;">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th data-sort="keyword">关键词</th>
                                <th data-sort="category">分类</th>
                                <th data-sort="status">状态</th>
                                <th data-sort="score">质量评分</th>
                                <th data-sort="time">完成时间</th>
                                <th data-sort="size">结果大小</th>
                                <th style="width: 150px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" name="resultSelect" value="1"></td>
                                <td>
                                    <div>
                                        <strong>人工智能芯片</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">AI Chip Technology</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-primary">科技</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 92%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">92</span>
                                    </div>
                                </td>
                                <td>2024-06-26 10:30</td>
                                <td>2.3 MB</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" data-tooltip="查看详情" onclick="viewResult(1)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" data-tooltip="下载" onclick="downloadResult(1)">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" data-tooltip="分享" onclick="shareResult(1)">
                                            <i class="fas fa-share"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除" onclick="deleteResult(1)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="resultSelect" value="2"></td>
                                <td>
                                    <div>
                                        <strong>新能源汽车</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">Electric Vehicle Market</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-warning">汽车</span></td>
                                <td><span class="badge badge-warning">处理中</span></td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 65%; background-color: var(--warning-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">-</span>
                                    </div>
                                </td>
                                <td>处理中...</td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-warning btn-sm" data-tooltip="暂停" onclick="pauseResult(2)">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="取消" onclick="cancelResult(2)">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="resultSelect" value="3"></td>
                                <td>
                                    <div>
                                        <strong>区块链技术</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">Blockchain Technology</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-info">金融科技</span></td>
                                <td><span class="badge badge-success">已完成</span></td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 88%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">88</span>
                                    </div>
                                </td>
                                <td>2024-06-26 09:15</td>
                                <td>1.8 MB</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-primary btn-sm" data-tooltip="查看详情" onclick="viewResult(3)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-success btn-sm" data-tooltip="下载" onclick="downloadResult(3)">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-secondary btn-sm" data-tooltip="分享" onclick="shareResult(3)">
                                            <i class="fas fa-share"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除" onclick="deleteResult(3)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" name="resultSelect" value="4"></td>
                                <td>
                                    <div>
                                        <strong>元宇宙平台</strong>
                                        <p style="font-size: 0.75rem; color: var(--text-secondary); margin: 0;">Metaverse Platform</p>
                                    </div>
                                </td>
                                <td><span class="badge badge-secondary">虚拟现实</span></td>
                                <td><span class="badge badge-danger">失败</span></td>
                                <td>
                                    <div class="d-flex items-center gap-2">
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 0%; background-color: var(--danger-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem;">-</span>
                                    </div>
                                </td>
                                <td>2024-06-26 08:45</td>
                                <td>-</td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-success btn-sm" data-tooltip="重试" onclick="retryResult(4)">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button class="btn btn-info btn-sm" data-tooltip="查看错误" onclick="viewError(4)">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </button>
                                        <button class="btn btn-danger btn-sm" data-tooltip="删除" onclick="deleteResult(4)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 卡片视图 -->
                <div id="cardView" style="display: none; padding: 1.5rem;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 1.5rem;">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-between items-start mb-3">
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">人工智能芯片</h4>
                                        <p style="color: var(--text-secondary); margin: 0.25rem 0; font-size: 0.875rem;">AI Chip Technology</p>
                                    </div>
                                    <span class="badge badge-success">已完成</span>
                                </div>
                                <div class="d-flex justify-between items-center mb-3">
                                    <span class="badge badge-primary">科技</span>
                                    <div class="d-flex items-center gap-2">
                                        <span style="font-size: 0.875rem;">质量评分:</span>
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 92%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem; font-weight: 500;">92</span>
                                    </div>
                                </div>
                                <p style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 1rem;">
                                    完成时间: 2024-06-26 10:30 | 大小: 2.3 MB
                                </p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                    <button class="btn btn-secondary btn-sm">
                                        <i class="fas fa-share"></i> 分享
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-between items-start mb-3">
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">新能源汽车</h4>
                                        <p style="color: var(--text-secondary); margin: 0.25rem 0; font-size: 0.875rem;">Electric Vehicle Market</p>
                                    </div>
                                    <span class="badge badge-warning">处理中</span>
                                </div>
                                <div class="d-flex justify-between items-center mb-3">
                                    <span class="badge badge-warning">汽车</span>
                                    <div class="d-flex items-center gap-2">
                                        <span style="font-size: 0.875rem;">进度:</span>
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 65%; background-color: var(--warning-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem; font-weight: 500;">65%</span>
                                    </div>
                                </div>
                                <p style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 1rem;">
                                    开始时间: 2024-06-26 11:00 | 预计完成: 2分钟
                                </p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-warning btn-sm">
                                        <i class="fas fa-pause"></i> 暂停
                                    </button>
                                    <button class="btn btn-danger btn-sm">
                                        <i class="fas fa-stop"></i> 取消
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-between items-start mb-3">
                                    <div>
                                        <h4 style="margin: 0; font-size: 1.125rem;">区块链技术</h4>
                                        <p style="color: var(--text-secondary); margin: 0.25rem 0; font-size: 0.875rem;">Blockchain Technology</p>
                                    </div>
                                    <span class="badge badge-success">已完成</span>
                                </div>
                                <div class="d-flex justify-between items-center mb-3">
                                    <span class="badge badge-info">金融科技</span>
                                    <div class="d-flex items-center gap-2">
                                        <span style="font-size: 0.875rem;">质量评分:</span>
                                        <div class="progress" style="width: 60px;">
                                            <div class="progress-bar" style="width: 88%; background-color: var(--success-color);"></div>
                                        </div>
                                        <span style="font-size: 0.875rem; font-weight: 500;">88</span>
                                    </div>
                                </div>
                                <p style="font-size: 0.875rem; color: var(--text-secondary); margin-bottom: 1rem;">
                                    完成时间: 2024-06-26 09:15 | 大小: 1.8 MB
                                </p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                    <button class="btn btn-secondary btn-sm">
                                        <i class="fas fa-share"></i> 分享
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分页 -->
            <div class="card-footer">
                <div class="table-pagination d-flex justify-between items-center">
                    <div class="d-flex items-center gap-2">
                        <span style="font-size: 0.875rem;">每页显示:</span>
                        <select class="form-control form-select" style="width: auto;" onchange="changePageSize(this.value)">
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="d-flex justify-center items-center gap-2">
                        <button class="btn btn-secondary btn-sm" disabled>
                            <i class="fas fa-chevron-left"></i> 上一页
                        </button>
                        <span class="btn btn-primary btn-sm">1</span>
                        <button class="btn btn-secondary btn-sm">2</button>
                        <button class="btn btn-secondary btn-sm">3</button>
                        <span>...</span>
                        <button class="btn btn-secondary btn-sm">125</button>
                        <button class="btn btn-secondary btn-sm">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端侧边栏遮罩 -->
    <div class="sidebar-overlay" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 30;"></div>

    <script src="assets/js/app.js"></script>
    <script>
        // 视图切换
        function toggleView(view) {
            const tableView = document.getElementById('tableView');
            const cardView = document.getElementById('cardView');
            const tableBtn = document.getElementById('tableViewBtn');
            const cardBtn = document.getElementById('cardViewBtn');

            if (view === 'table') {
                tableView.style.display = 'block';
                cardView.style.display = 'none';
                tableBtn.classList.add('btn-primary');
                tableBtn.classList.remove('btn-secondary');
                cardBtn.classList.add('btn-secondary');
                cardBtn.classList.remove('btn-primary');
            } else {
                tableView.style.display = 'none';
                cardView.style.display = 'block';
                cardBtn.classList.add('btn-primary');
                cardBtn.classList.remove('btn-secondary');
                tableBtn.classList.add('btn-secondary');
                tableBtn.classList.remove('btn-primary');
            }
        }

        // 筛选功能
        function applyFilters() {
            AIResearchApp.showNotification('筛选已应用', 'success');
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('timeFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            AIResearchApp.showNotification('筛选已重置', 'info');
        }

        // 结果操作
        function viewResult(id) {
            AIResearchApp.showNotification(`正在查看结果 ${id}`, 'info');
        }

        function downloadResult(id) {
            AIResearchApp.showNotification(`开始下载结果 ${id}`, 'success');
        }

        function shareResult(id) {
            AIResearchApp.showNotification(`分享链接已复制到剪贴板`, 'success');
        }

        function deleteResult(id) {
            if (confirm('确定要删除这个结果吗？')) {
                AIResearchApp.showNotification(`结果 ${id} 已删除`, 'warning');
            }
        }

        function pauseResult(id) {
            AIResearchApp.showNotification(`任务 ${id} 已暂停`, 'info');
        }

        function cancelResult(id) {
            if (confirm('确定要取消这个任务吗？')) {
                AIResearchApp.showNotification(`任务 ${id} 已取消`, 'warning');
            }
        }

        function retryResult(id) {
            AIResearchApp.showNotification(`正在重试任务 ${id}`, 'info');
        }

        function viewError(id) {
            AIResearchApp.showNotification(`查看错误详情功能开发中`, 'info');
        }

        // 批量操作
        function exportSelected() {
            const selected = document.querySelectorAll('input[name="resultSelect"]:checked');
            if (selected.length === 0) {
                AIResearchApp.showNotification('请先选择要导出的结果', 'warning');
                return;
            }
            AIResearchApp.showNotification(`正在导出 ${selected.length} 个结果`, 'success');
        }

        // 分页
        function changePageSize(size) {
            AIResearchApp.showNotification(`每页显示 ${size} 条记录`, 'info');
        }

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="resultSelect"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            // 实时搜索功能
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    </script>
</body>
</html>
