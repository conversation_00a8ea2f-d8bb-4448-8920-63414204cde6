// AI调研助手 - 主要JavaScript交互脚本

// 全局应用对象
const AIResearchApp = {
  // 初始化应用
  init() {
    this.initSidebar();
    this.initTooltips();
    this.initModals();
    this.initTables();
    this.initForms();
    this.initCharts();
    this.bindEvents();
    console.log('AI调研助手应用已初始化');
  },

  // 侧边栏控制
  initSidebar() {
    const sidebarToggle = document.querySelector('[data-sidebar-toggle]');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (sidebarToggle && sidebar) {
      sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('open');
        if (overlay) overlay.classList.toggle('active');
      });
    }

    if (overlay) {
      overlay.addEventListener('click', () => {
        sidebar.classList.remove('open');
        overlay.classList.remove('active');
      });
    }

    // 高亮当前页面导航
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      if (link.getAttribute('href') === currentPath.split('/').pop()) {
        link.classList.add('active');
      }
    });
  },

  // 工具提示初始化
  initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
      element.addEventListener('mouseenter', (e) => {
        this.showTooltip(e.target, e.target.getAttribute('data-tooltip'));
      });
      element.addEventListener('mouseleave', () => {
        this.hideTooltip();
      });
    });
  },

  // 显示工具提示
  showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
      position: absolute;
      background: #1f2937;
      color: white;
      padding: 0.5rem;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      z-index: 1000;
      pointer-events: none;
      white-space: nowrap;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    
    setTimeout(() => tooltip.classList.add('fade-in'), 10);
  },

  // 隐藏工具提示
  hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
      tooltip.remove();
    }
  },

  // 模态框初始化
  initModals() {
    const modalTriggers = document.querySelectorAll('[data-modal-target]');
    const modalCloses = document.querySelectorAll('[data-modal-close]');
    
    modalTriggers.forEach(trigger => {
      trigger.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = trigger.getAttribute('data-modal-target');
        const modal = document.getElementById(targetId);
        if (modal) this.showModal(modal);
      });
    });

    modalCloses.forEach(close => {
      close.addEventListener('click', (e) => {
        e.preventDefault();
        const modal = close.closest('.modal');
        if (modal) this.hideModal(modal);
      });
    });

    // 点击背景关闭模态框
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('modal-backdrop')) {
        this.hideModal(e.target.closest('.modal'));
      }
    });
  },

  // 显示模态框
  showModal(modal) {
    modal.style.display = 'flex';
    setTimeout(() => modal.classList.add('active'), 10);
    document.body.style.overflow = 'hidden';
  },

  // 隐藏模态框
  hideModal(modal) {
    modal.classList.remove('active');
    setTimeout(() => {
      modal.style.display = 'none';
      document.body.style.overflow = '';
    }, 300);
  },

  // 表格功能初始化
  initTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
      this.initTableSort(table);
      this.initTableFilter(table);
      this.initTablePagination(table);
    });
  },

  // 表格排序
  initTableSort(table) {
    const headers = table.querySelectorAll('th[data-sort]');
    headers.forEach(header => {
      header.style.cursor = 'pointer';
      header.addEventListener('click', () => {
        const column = header.getAttribute('data-sort');
        const direction = header.classList.contains('sort-asc') ? 'desc' : 'asc';
        this.sortTable(table, column, direction);
        
        // 更新排序指示器
        headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
        header.classList.add(`sort-${direction}`);
      });
    });
  },

  // 表格排序实现
  sortTable(table, column, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => 
      th.getAttribute('data-sort') === column
    );

    rows.sort((a, b) => {
      const aValue = a.cells[columnIndex].textContent.trim();
      const bValue = b.cells[columnIndex].textContent.trim();
      
      if (direction === 'asc') {
        return aValue.localeCompare(bValue, undefined, { numeric: true });
      } else {
        return bValue.localeCompare(aValue, undefined, { numeric: true });
      }
    });

    rows.forEach(row => tbody.appendChild(row));
  },

  // 表格筛选
  initTableFilter(table) {
    const filterInput = table.parentElement.querySelector('.table-filter');
    if (filterInput) {
      filterInput.addEventListener('input', (e) => {
        this.filterTable(table, e.target.value);
      });
    }
  },

  // 表格筛选实现
  filterTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();

    rows.forEach(row => {
      const text = row.textContent.toLowerCase();
      row.style.display = text.includes(term) ? '' : 'none';
    });
  },

  // 表格分页
  initTablePagination(table) {
    const pagination = table.parentElement.querySelector('.table-pagination');
    if (pagination) {
      const pageSize = parseInt(pagination.getAttribute('data-page-size')) || 10;
      this.paginateTable(table, pageSize);
    }
  },

  // 表格分页实现
  paginateTable(table, pageSize) {
    const rows = Array.from(table.querySelectorAll('tbody tr'));
    const totalPages = Math.ceil(rows.length / pageSize);
    let currentPage = 1;

    const showPage = (page) => {
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      
      rows.forEach((row, index) => {
        row.style.display = (index >= start && index < end) ? '' : 'none';
      });
    };

    // 创建分页控件
    const paginationContainer = table.parentElement.querySelector('.table-pagination');
    if (paginationContainer && totalPages > 1) {
      paginationContainer.innerHTML = this.createPaginationHTML(currentPage, totalPages);
      
      // 绑定分页事件
      paginationContainer.addEventListener('click', (e) => {
        if (e.target.classList.contains('page-link')) {
          e.preventDefault();
          const page = parseInt(e.target.getAttribute('data-page'));
          if (page && page !== currentPage) {
            currentPage = page;
            showPage(currentPage);
            paginationContainer.innerHTML = this.createPaginationHTML(currentPage, totalPages);
          }
        }
      });
    }

    showPage(currentPage);
  },

  // 创建分页HTML
  createPaginationHTML(currentPage, totalPages) {
    let html = '<div class="d-flex justify-center items-center gap-2">';
    
    // 上一页
    if (currentPage > 1) {
      html += `<a href="#" class="page-link btn btn-secondary btn-sm" data-page="${currentPage - 1}">上一页</a>`;
    }
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
      if (i === currentPage) {
        html += `<span class="btn btn-primary btn-sm">${i}</span>`;
      } else {
        html += `<a href="#" class="page-link btn btn-secondary btn-sm" data-page="${i}">${i}</a>`;
      }
    }
    
    // 下一页
    if (currentPage < totalPages) {
      html += `<a href="#" class="page-link btn btn-secondary btn-sm" data-page="${currentPage + 1}">下一页</a>`;
    }
    
    html += '</div>';
    return html;
  },

  // 表单功能初始化
  initForms() {
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        if (!this.validateForm(form)) {
          e.preventDefault();
        }
      });
    });

    // 文件上传处理
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
      input.addEventListener('change', (e) => {
        this.handleFileUpload(e.target);
      });
    });
  },

  // 表单验证
  validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, '此字段为必填项');
        isValid = false;
      } else {
        this.clearFieldError(field);
      }
    });

    return isValid;
  },

  // 显示字段错误
  showFieldError(field, message) {
    this.clearFieldError(field);
    const error = document.createElement('div');
    error.className = 'field-error text-danger mt-1';
    error.style.fontSize = '0.75rem';
    error.textContent = message;
    field.parentNode.appendChild(error);
    field.classList.add('error');
  },

  // 清除字段错误
  clearFieldError(field) {
    const error = field.parentNode.querySelector('.field-error');
    if (error) error.remove();
    field.classList.remove('error');
  },

  // 文件上传处理
  handleFileUpload(input) {
    const file = input.files[0];
    if (file) {
      const preview = input.parentElement.querySelector('.file-preview');
      if (preview) {
        preview.textContent = `已选择: ${file.name} (${this.formatFileSize(file.size)})`;
      }
    }
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 图表初始化（简单实现）
  initCharts() {
    const chartElements = document.querySelectorAll('[data-chart]');
    chartElements.forEach(element => {
      const type = element.getAttribute('data-chart');
      const data = JSON.parse(element.getAttribute('data-chart-data') || '{}');
      this.renderChart(element, type, data);
    });
  },

  // 简单图表渲染
  renderChart(element, type, data) {
    if (type === 'progress') {
      const percentage = data.percentage || 0;
      element.innerHTML = `
        <div class="progress">
          <div class="progress-bar" style="width: ${percentage}%"></div>
        </div>
        <div class="text-center mt-2">${percentage}%</div>
      `;
    }
  },

  // 绑定全局事件
  bindEvents() {
    // 全局加载状态
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('btn-loading')) {
        this.showLoading(e.target);
      }
    });

    // 复制到剪贴板
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('copy-btn')) {
        const text = e.target.getAttribute('data-copy');
        this.copyToClipboard(text);
      }
    });

    // 确认对话框
    document.addEventListener('click', (e) => {
      if (e.target.hasAttribute('data-confirm')) {
        const message = e.target.getAttribute('data-confirm');
        if (!confirm(message)) {
          e.preventDefault();
        }
      }
    });
  },

  // 显示加载状态
  showLoading(button) {
    const originalText = button.textContent;
    button.textContent = '处理中...';
    button.disabled = true;
    button.classList.add('loading');

    setTimeout(() => {
      button.textContent = originalText;
      button.disabled = false;
      button.classList.remove('loading');
    }, 2000);
  },

  // 复制到剪贴板
  copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      this.showNotification('已复制到剪贴板', 'success');
    }).catch(() => {
      this.showNotification('复制失败', 'error');
    });
  },

  // 显示通知
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      color: white;
      z-index: 1000;
      animation: slideIn 0.3s ease-in-out;
    `;
    
    const colors = {
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6'
    };
    
    notification.style.backgroundColor = colors[type] || colors.info;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in-out';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  },

  // 工具函数
  utils: {
    // 防抖函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');

      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
    }
  }
};

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
  AIResearchApp.init();
});

// 导出到全局作用域
window.AIResearchApp = AIResearchApp;
