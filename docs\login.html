<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - AI调研助手</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            position: relative;
            z-index: 1;
        }

        .login-left {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="20" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.5;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .login-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .login-left p {
            opacity: 0.9;
            font-size: 1.125rem;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .login-right {
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h2 {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-group {
            position: relative;
        }

        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            z-index: 1;
        }

        .form-control {
            padding-left: 3rem;
            height: 3rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .forgot-password {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .login-btn {
            height: 3rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .login-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-accounts {
            margin-top: 2rem;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-light);
        }

        .demo-accounts h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
            text-align: center;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            border: 1px solid var(--border-light);
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .demo-account-info {
            display: flex;
            flex-direction: column;
        }

        .demo-account-role {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--text-primary);
        }

        .demo-account-credentials {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
        }

        .demo-login-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-login-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            display: none;
        }

        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                max-width: 400px;
            }

            .login-left {
                padding: 2rem;
            }

            .login-right {
                padding: 2rem;
            }

            .login-left h1 {
                font-size: 1.5rem;
            }

            .login-left p {
                font-size: 1rem;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- 左侧品牌区域 -->
        <div class="login-left">
            <div class="login-logo">
                <i class="fas fa-brain"></i>
            </div>
            <h1>AI调研助手</h1>
            <p>智能数据分析平台<br>为您的调研工作提供强大的AI支持</p>
        </div>

        <!-- 右侧登录表单 -->
        <div class="login-right">
            <div class="login-header">
                <h2>欢迎回来</h2>
                <p>请登录您的账户以继续使用</p>
            </div>

            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText"></span>
            </div>

            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i>
                <span id="successText"></span>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" class="form-control" id="username" placeholder="用户名" required>
                </div>

                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" class="form-control" id="password" placeholder="密码" required>
                </div>

                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span>记住我</span>
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>登录</span>
                </button>
            </form>

            <!-- 演示账户 -->
            <div class="demo-accounts">
                <h3><i class="fas fa-info-circle"></i> 演示账户</h3>
                
                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-role">
                            <i class="fas fa-crown" style="color: #f59e0b; margin-right: 0.25rem;"></i>
                            管理员账户
                        </div>
                        <div class="demo-account-credentials">用户名: admin / 密码: admin</div>
                    </div>
                    <button class="demo-login-btn" onclick="quickLogin('admin', 'admin')">
                        快速登录
                    </button>
                </div>

                <div class="demo-account">
                    <div class="demo-account-info">
                        <div class="demo-account-role">
                            <i class="fas fa-user" style="color: #3b82f6; margin-right: 0.25rem;"></i>
                            普通用户账户
                        </div>
                        <div class="demo-account-credentials">用户名: user / 密码: user</div>
                    </div>
                    <button class="demo-login-btn" onclick="quickLogin('user', 'user')">
                        快速登录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/auth.js"></script>
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            performLogin(username, password, rememberMe);
        });

        // 执行登录
        function performLogin(username, password, rememberMe = false) {
            const loginBtn = document.getElementById('loginBtn');
            const originalText = loginBtn.innerHTML;
            
            // 显示加载状态
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
            loginBtn.disabled = true;
            
            // 隐藏之前的消息
            hideMessages();
            
            // 模拟网络延迟
            setTimeout(() => {
                const result = window.AuthManager.login(username, password);
                
                if (result.success) {
                    showSuccessMessage('登录成功，正在跳转...');
                    
                    // 记录登录活动
                    window.AuthManager.logActivity('用户登录', {
                        username: username,
                        rememberMe: rememberMe
                    });
                    
                    // 跳转到仪表板
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    showErrorMessage(result.message);
                    loginBtn.innerHTML = originalText;
                    loginBtn.disabled = false;
                }
            }, 1000);
        }

        // 快速登录
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            performLogin(username, password);
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 3000);
        }

        // 显示成功消息
        function showSuccessMessage(message) {
            const successDiv = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            successText.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏所有消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 忘记密码处理
        document.querySelector('.forgot-password').addEventListener('click', function(e) {
            e.preventDefault();
            alert('请联系系统管理员重置密码');
        });

        // 页面加载时的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 如果用户已登录，直接跳转
            if (window.AuthManager.isLoggedIn()) {
                window.location.href = 'dashboard.html';
                return;
            }

            // 添加输入框焦点效果
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.querySelector('i').style.color = 'var(--primary-color)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.querySelector('i').style.color = 'var(--text-muted)';
                });
            });

            // 键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl+Enter 快速登录管理员账户
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    quickLogin('admin', 'admin');
                }
                // Alt+Enter 快速登录普通用户账户
                else if (e.altKey && e.key === 'Enter') {
                    e.preventDefault();
                    quickLogin('user', 'user');
                }
            });
        });
    </script>
</body>
</html>
